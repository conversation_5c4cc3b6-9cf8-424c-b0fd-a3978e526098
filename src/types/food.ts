export interface FoodItem {
  id: string;
  name: string;
  price: number;
  type: 'side' | 'main';
}

export interface OrderItem {
  foodItem: FoodItem;
  quantity: number;
}

export interface Order {
  id: string;
  customerName: string;
  items: OrderItem[];
  totalAmount: number;
  createdAt: Date;
  isPaid: boolean;
}

export interface DailyStats {
  totalRevenue: number;
  totalOrders: number;
  totalDebt: number;
  paidOrders: number;
  unpaidOrders: number;
  topItems: Array<{
    item: FoodItem;
    quantity: number;
  }>;
}

// Database-specific types for dashboard
export interface DatabaseUser {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseProduct {
  id: string;
  name: string;
  price: number;
  category: 'main' | 'side';
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseOrder {
  id: string;
  user_id: string;
  total_price: number;
  is_paid: boolean;
  order_date: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseOrderItem {
  id: string;
  order_id: string;
  product_id: string;
  quantity: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseDebt {
  id: string;
  user_id: string;
  amount: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface DatabaseOrderHistory {
  id: string;
  user_name: string;
  action_type: string;
  order_id: string | null;
  item_name: string | null;
  item_quantity: number | null;
  total_amount: number | null;
  description: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

// Dashboard-specific aggregated data types
export interface DashboardStats {
  totalRevenue: number;
  totalOrders: number;
  totalDebt: number;
  paidOrders: number;
  unpaidOrders: number;
  topItems: Array<{
    product: DatabaseProduct;
    quantity: number;
    revenue: number;
  }>;
}

export interface CategoryData {
  name: string;
  value: number;
  color: string;
}

export interface WeeklyOrderData {
  day: string;
  orders: number;
  revenue: number;
}

export interface DashboardData {
  stats: DashboardStats;
  categoryData: CategoryData[];
  weeklyData: WeeklyOrderData[];
  isLoading: boolean;
  error: string | null;
}