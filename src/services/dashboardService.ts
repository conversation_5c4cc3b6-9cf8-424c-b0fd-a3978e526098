import { supabase } from "@/integrations/supabase/client";
import {
  DashboardStats,
  CategoryData,
  WeeklyOrderData,
  DashboardData,
  DatabaseProduct
} from "@/types/food";

export interface DashboardService {
  getDashboardData: () => Promise<DashboardData>;
  getTodayStats: () => Promise<DashboardStats>;
  getCategoryData: () => Promise<CategoryData[]>;
  getWeeklyOrderData: () => Promise<WeeklyOrderData[]>;
  getTotalDebt: () => Promise<number>;
}

// Helper function to get today's date range
const getTodayDateRange = () => {
  const today = new Date();
  const startOfDay = new Date(today);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(today);
  endOfDay.setHours(23, 59, 59, 999);
  
  return {
    start: startOfDay.toISOString(),
    end: endOfDay.toISOString()
  };
};

// Helper function to get week date range
const getWeekDateRange = () => {
  const today = new Date();
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - 6); // Last 7 days
  startOfWeek.setHours(0, 0, 0, 0);
  
  return {
    start: startOfWeek.toISOString(),
    end: today.toISOString()
  };
};

export const dashboardService: DashboardService = {
  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(): Promise<DashboardData> {
    try {
      // Use Promise.allSettled to handle partial failures gracefully
      const [statsResult, categoryResult, weeklyResult] = await Promise.allSettled([
        this.getTodayStats(),
        this.getCategoryData(),
        this.getWeeklyOrderData()
      ]);

      const stats = statsResult.status === 'fulfilled' ? statsResult.value : {
        totalRevenue: 0,
        totalOrders: 0,
        totalDebt: 0,
        paidOrders: 0,
        unpaidOrders: 0,
        topItems: []
      };

      const categoryData = categoryResult.status === 'fulfilled' ? categoryResult.value : [];
      const weeklyData = weeklyResult.status === 'fulfilled' ? weeklyResult.value : [];

      // Collect any errors
      const errors = [statsResult, categoryResult, weeklyResult]
        .filter(result => result.status === 'rejected')
        .map(result => (result as PromiseRejectedResult).reason.message);

      return {
        stats,
        categoryData,
        weeklyData,
        isLoading: false,
        error: errors.length > 0 ? `Một số dữ liệu không thể tải: ${errors.join(', ')}` : null
      };
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      return {
        stats: {
          totalRevenue: 0,
          totalOrders: 0,
          totalDebt: 0,
          paidOrders: 0,
          unpaidOrders: 0,
          topItems: []
        },
        categoryData: [],
        weeklyData: [],
        isLoading: false,
        error: error instanceof Error ? error.message : "Không thể kết nối đến cơ sở dữ liệu"
      };
    }
  },

  /**
   * Get today's statistics
   */
  async getTodayStats(): Promise<DashboardStats> {
    try {
      const { start, end } = getTodayDateRange();

      // Fetch today's orders with related data
      const { data: orders, error: ordersError } = await supabase
        .from("orders")
        .select(`
          id,
          total_price,
          is_paid,
          order_date,
          created_at,
          users!orders_user_id_fkey(name),
          order_items(
            quantity,
            products(id, name, price, category)
          )
        `)
        .gte("created_at", start)
        .lte("created_at", end)
        .is("deleted_at", null);

      if (ordersError) {
        throw new Error(`Failed to fetch orders: ${ordersError.message}`);
      }

      const todayOrders = orders || [];
      
      // Calculate basic stats
      const totalRevenue = todayOrders.reduce((sum, order) => sum + order.total_price, 0);
      const totalOrders = todayOrders.length;
      const paidOrders = todayOrders.filter(order => order.is_paid).length;
      const unpaidOrders = totalOrders - paidOrders;

      // Get total debt from debts table
      const totalDebt = await this.getTotalDebt();

      // Calculate top items
      const itemCounts = new Map<string, { product: DatabaseProduct; quantity: number; revenue: number }>();
      
      todayOrders.forEach(order => {
        order.order_items?.forEach(item => {
          if (item.products) {
            const productId = item.products.id;
            const existing = itemCounts.get(productId);
            const revenue = item.quantity * item.products.price;
            
            if (existing) {
              existing.quantity += item.quantity;
              existing.revenue += revenue;
            } else {
              itemCounts.set(productId, {
                product: item.products as DatabaseProduct,
                quantity: item.quantity,
                revenue: revenue
              });
            }
          }
        });
      });

      const topItems = Array.from(itemCounts.values())
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 5);

      return {
        totalRevenue,
        totalOrders,
        totalDebt,
        paidOrders,
        unpaidOrders,
        topItems
      };
    } catch (error) {
      console.error("Error fetching today's stats:", error);
      throw error;
    }
  },

  /**
   * Get category distribution data for pie chart
   */
  async getCategoryData(): Promise<CategoryData[]> {
    try {
      const { start, end } = getTodayDateRange();

      const { data: orderItems, error } = await supabase
        .from("order_items")
        .select(`
          quantity,
          products(name, category),
          orders!order_items_order_id_fkey(created_at, deleted_at)
        `)
        .is("deleted_at", null);

      if (error) {
        throw new Error(`Failed to fetch category data: ${error.message}`);
      }

      // Filter for today's orders and aggregate by category
      const categoryMap = new Map<string, number>();
      const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff88', '#8dd1e1'];
      
      orderItems?.forEach(item => {
        if (item.orders && !item.orders.deleted_at && 
            item.orders.created_at >= start && item.orders.created_at <= end) {
          const category = item.products?.category || 'other';
          const current = categoryMap.get(category) || 0;
          categoryMap.set(category, current + item.quantity);
        }
      });

      return Array.from(categoryMap.entries()).map(([name, value], index) => ({
        name: name === 'main' ? 'Món chính' : name === 'side' ? 'Món phụ' : name,
        value,
        color: colors[index % colors.length]
      }));
    } catch (error) {
      console.error("Error fetching category data:", error);
      return [];
    }
  },

  /**
   * Get weekly order data for bar chart
   */
  async getWeeklyOrderData(): Promise<WeeklyOrderData[]> {
    try {
      const { start } = getWeekDateRange();

      const { data: orders, error } = await supabase
        .from("orders")
        .select("created_at, total_price")
        .gte("created_at", start)
        .is("deleted_at", null);

      if (error) {
        throw new Error(`Failed to fetch weekly data: ${error.message}`);
      }

      // Group by day
      const dayMap = new Map<string, { orders: number; revenue: number }>();
      const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
      
      orders?.forEach(order => {
        const date = new Date(order.created_at);
        const dayIndex = date.getDay();
        const dayName = dayNames[dayIndex];
        
        const existing = dayMap.get(dayName) || { orders: 0, revenue: 0 };
        dayMap.set(dayName, {
          orders: existing.orders + 1,
          revenue: existing.revenue + order.total_price
        });
      });

      // Return data for all days of the week
      return dayNames.map(day => ({
        day,
        orders: dayMap.get(day)?.orders || 0,
        revenue: dayMap.get(day)?.revenue || 0
      }));
    } catch (error) {
      console.error("Error fetching weekly data:", error);
      return [];
    }
  },

  /**
   * Get total debt amount
   */
  async getTotalDebt(): Promise<number> {
    try {
      const { data: debts, error } = await supabase
        .from("debts")
        .select("amount")
        .is("deleted_at", null);

      if (error) {
        throw new Error(`Failed to fetch debt data: ${error.message}`);
      }

      return debts?.reduce((sum, debt) => sum + debt.amount, 0) || 0;
    } catch (error) {
      console.error("Error fetching total debt:", error);
      return 0;
    }
  }
};
