import { supabase } from "@/integrations/supabase/client";
import { Database, TablesInsert } from "@/integrations/supabase/types";
import { Order, OrderItem, FoodItem } from "@/types/food";

// Type aliases for better readability
type OrderInsert = TablesInsert<"orders">;
type OrderItemInsert = TablesInsert<"order_items">;
type UserInsert = TablesInsert<"users">;
type ProductInsert = TablesInsert<"products">;

export interface SaveOrderResult {
  success: boolean;
  order?: Order;
  error?: string;
}

export interface OrderData {
  id: string;
  userName: string;
  dishes: string[];
  totalAmount: number;
  isPaid: boolean;
  orderDate: string;
}

export interface DishSummaryData {
  dishName: string;
  quantity: number;
  totalRevenue: number;
}

// Raw Supabase order data structure
export interface RawSupabaseOrder {
  id: string;
  user_id: string;
  total_price: number;
  is_paid: boolean | null;
  order_date: string | null;
  created_at: string | null;
  updated_at: string | null;
  deleted_at: string | null;
  users: {
    name: string;
  } | null;
  order_items: Array<{
    quantity: number;
    products: {
      id: string;
      name: string;
      price: number;
      category: string;
    } | null;
  }> | null;
}

// Raw order item structure for getAllOrders
export interface RawOrderItem {
  quantity: number;
  products: {
    name: string;
  } | null;
}

// Raw order structure for getAllOrders (simplified)
export interface RawOrderForList {
  id: string;
  total_price: number;
  is_paid: boolean | null;
  order_date: string | null;
  created_at: string | null;
  users: {
    name: string;
  } | null;
  order_items: RawOrderItem[] | null;
}

// Raw order item structure for dish summary
export interface RawOrderItemForSummary {
  quantity: number;
  products: {
    name: string;
    price: number;
  } | null;
  orders: {
    deleted_at: string | null;
  } | null;
}

export interface OrderFilterOptions {
  dateFilter?: string; // YYYY-MM-DD format for specific date
  dateRange?: { startDate: string; endDate: string }; // For week/range filtering
  paymentStatusFilter?: 'all' | 'paid' | 'unpaid';
}

export interface GetOrdersResult {
  success: boolean;
  orders?: OrderData[];
  error?: string;
}

export interface GetDishSummaryResult {
  success: boolean;
  dishSummary?: DishSummaryData[];
  error?: string;
}

export type SupabaseOrderService = {
  saveOrderToDatabase: (customerName: string, items: OrderItem[], totalAmount: number) => Promise<SaveOrderResult>;
  ensureUserExists: (userName: string) => Promise<{ success: boolean; userId?: string; error?: string }>;
  ensureProductsExist: (foodItems: FoodItem[]) => Promise<{ success: boolean; productMap?: Map<string, string>; error?: string }>;
  getOrderById: (orderId: string) => Promise<{ success: boolean; order?: RawSupabaseOrder; error?: string }>;
  getAllOrders: (filterOptions?: OrderFilterOptions) => Promise<GetOrdersResult>;
  getDishSummary: (filterOptions?: OrderFilterOptions) => Promise<GetDishSummaryResult>;
  updatePaymentStatus: (orderIds: string[], isPaid: boolean) => Promise<{ success: boolean; error?: string }>;
}

export const supabaseOrderService: SupabaseOrderService = {
  /**
   * Ensures a user exists in the database, creates if not found
   */
  async ensureUserExists(userName: string) {
    try {
      // First, try to find existing user
      const { data: existingUser, error: findError } = await supabase
        .from("users")
        .select("id")
        .eq("name", userName)
        .is("deleted_at", null)
        .single();

      if (findError && findError.code !== "PGRST116") {
        // PGRST116 is "not found" error, which is expected
        console.error("Error finding user:", findError);
        return { success: false, error: `Failed to find user: ${findError.message}` };
      }

      if (existingUser) {
        return { success: true, userId: existingUser.id };
      }

      // User doesn't exist, create new one
      const userInsert: UserInsert = {
        name: userName,
      };

      const { data: newUser, error: createError } = await supabase
        .from("users")
        .insert(userInsert)
        .select("id")
        .single();

      if (createError) {
        console.error("Error creating user:", createError);
        return { success: false, error: `Failed to create user: ${createError.message}` };
      }

      return { success: true, userId: newUser.id };
    } catch (error) {
      console.error("Unexpected error in ensureUserExists:", error);
      return { success: false, error: "Unexpected error occurred while managing user" };
    }
  },

  /**
   * Ensures products exist in the database, creates missing ones
   */
  async ensureProductsExist(foodItems: FoodItem[]) {
    try {
      const productMap = new Map<string, string>();

      // Get existing products
      const { data: existingProducts, error: fetchError } = await supabase
        .from("products")
        .select("id, name")
        .is("deleted_at", null);

      if (fetchError) {
        console.error("Error fetching products:", fetchError);
        return { success: false, error: `Failed to fetch products: ${fetchError.message}` };
      }

      // Create a map of existing products by name
      const existingProductMap = new Map<string, string>();
      existingProducts?.forEach(product => {
        existingProductMap.set(product.name, product.id);
      });

      // Identify missing products
      const missingProducts: ProductInsert[] = [];
      
      for (const foodItem of foodItems) {
        if (existingProductMap.has(foodItem.name)) {
          productMap.set(foodItem.id, existingProductMap.get(foodItem.name)!);
        } else {
          missingProducts.push({
            name: foodItem.name,
            price: foodItem.price,
            category: foodItem.type as "main" | "side",
          });
        }
      }

      // Insert missing products if any
      if (missingProducts.length > 0) {
        const { data: newProducts, error: insertError } = await supabase
          .from("products")
          .insert(missingProducts)
          .select("id, name");

        if (insertError) {
          console.error("Error creating products:", insertError);
          return { success: false, error: `Failed to create products: ${insertError.message}` };
        }

        // Add new products to the map
        newProducts?.forEach(product => {
          const foodItem = foodItems.find(item => item.name === product.name);
          if (foodItem) {
            productMap.set(foodItem.id, product.id);
          }
        });
      }

      return { success: true, productMap };
    } catch (error) {
      console.error("Unexpected error in ensureProductsExist:", error);
      return { success: false, error: "Unexpected error occurred while managing products" };
    }
  },

  /**
   * Saves an order to the Supabase database
   */
  async saveOrderToDatabase(customerName: string, items: OrderItem[], totalAmount: number): Promise<SaveOrderResult> {
    try {
      // Step 1: Ensure user exists
      const userResult = await this.ensureUserExists(customerName);
      if (!userResult.success || !userResult.userId) {
        return { success: false, error: userResult.error || "Failed to create/find user" };
      }

      // Step 2: Ensure products exist
      const foodItems = items.map(item => item.foodItem);
      const productsResult = await this.ensureProductsExist(foodItems);
      if (!productsResult.success || !productsResult.productMap) {
        return { success: false, error: productsResult.error || "Failed to create/find products" };
      }

      // Step 3: Create order
      const orderInsert: OrderInsert = {
        user_id: userResult.userId,
        total_price: totalAmount,
        is_paid: false,
        order_date: new Date().toISOString(),
      };

      const { data: newOrder, error: orderError } = await supabase
        .from("orders")
        .insert(orderInsert)
        .select("*")
        .single();

      if (orderError) {
        console.error("Error creating order:", orderError);
        return { success: false, error: `Failed to create order: ${orderError.message}` };
      }

      // Step 4: Create order items
      const orderItemsInsert: OrderItemInsert[] = items.map(item => {
        const productId = productsResult.productMap!.get(item.foodItem.id);
        if (!productId) {
          throw new Error(`Product ID not found for food item: ${item.foodItem.name}`);
        }
        
        return {
          order_id: newOrder.id,
          product_id: productId,
          quantity: item.quantity,
        };
      });

      const { error: itemsError } = await supabase
        .from("order_items")
        .insert(orderItemsInsert);

      if (itemsError) {
        console.error("Error creating order items:", itemsError);
        // Try to clean up the order if order items failed
        await supabase.from("orders").delete().eq("id", newOrder.id);
        return { success: false, error: `Failed to create order items: ${itemsError.message}` };
      }

      // Step 5: Return success with order data
      const order: Order = {
        id: newOrder.id,
        customerName: customerName,
        items: items,
        totalAmount: totalAmount,
        createdAt: new Date(newOrder.created_at || new Date()),
        isPaid: newOrder.is_paid || false,
      };

      return { success: true, order };
    } catch (error) {
      console.error("Unexpected error in saveOrderToDatabase:", error);
      return { success: false, error: "Unexpected error occurred while saving order" };
    }
  },

  /**
   * Retrieves an order by ID from the database
   */
  async getOrderById(orderId: string) {
    try {
      const { data: order, error } = await supabase
        .from("orders")
        .select(`
          *,
          users!orders_user_id_fkey(name),
          order_items(
            quantity,
            products(id, name, price, category)
          )
        `)
        .eq("id", orderId)
        .is("deleted_at", null)
        .single();

      if (error) {
        console.error("Error fetching order:", error);
        return { success: false, error: `Failed to fetch order: ${error.message}` };
      }

      return { success: true, order };
    } catch (error) {
      console.error("Unexpected error in getOrderById:", error);
      return { success: false, error: "Unexpected error occurred while fetching order" };
    }
  },

  /**
   * Retrieves all orders from the database with user and product information
   * Supports date filtering and payment status filtering
   */
  async getAllOrders(filterOptions?: OrderFilterOptions): Promise<GetOrdersResult> {
    try {
      let query = supabase
        .from("orders")
        .select(`
          id,
          total_price,
          is_paid,
          order_date,
          created_at,
          users!orders_user_id_fkey(name),
          order_items(
            quantity,
            products(name)
          )
        `)
        .is("deleted_at", null);

      // Apply date filtering
      if (filterOptions?.dateFilter) {
        // Filter by specific date (YYYY-MM-DD)
        query = query.gte("order_date", `${filterOptions.dateFilter}T00:00:00.000Z`)
                    .lt("order_date", `${filterOptions.dateFilter}T23:59:59.999Z`);
      } else if (filterOptions?.dateRange) {
        // Filter by date range (for week filtering)
        query = query.gte("order_date", `${filterOptions.dateRange.startDate}T00:00:00.000Z`)
                    .lte("order_date", `${filterOptions.dateRange.endDate}T23:59:59.999Z`);
      }

      // Apply payment status filtering
      if (filterOptions?.paymentStatusFilter && filterOptions.paymentStatusFilter !== 'all') {
        const isPaid = filterOptions.paymentStatusFilter === 'paid';
        query = query.eq("is_paid", isPaid);
      }

      // Execute query with ordering
      const { data: orders, error } = await query.order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching orders:", error);
        return { success: false, error: `Failed to fetch orders: ${error.message}` };
      }

      if (!orders) {
        return { success: true, orders: [] };
      }

      // Transform the data to match the expected format
      const transformedOrders: OrderData[] = orders.map((order: RawOrderForList) => ({
        id: order.id,
        userName: order.users?.name || "Unknown",
        dishes: order.order_items?.map((item: RawOrderItem) => item.products?.name).filter(Boolean) || [],
        totalAmount: order.total_price,
        isPaid: order.is_paid || false,
        orderDate: order.order_date || order.created_at,
      }));

      return { success: true, orders: transformedOrders };
    } catch (error) {
      console.error("Unexpected error in getAllOrders:", error);
      return { success: false, error: "Unexpected error occurred while fetching orders" };
    }
  },

  /**
   * Retrieves dish summary data with quantities and total revenue
   * Supports date filtering and payment status filtering
   */
  async getDishSummary(filterOptions?: OrderFilterOptions): Promise<GetDishSummaryResult> {
    try {
      const query = supabase
        .from("order_items")
        .select(`
          quantity,
          products(name, price),
          orders!order_items_order_id_fkey(deleted_at, order_date, is_paid)
        `)
        .is("deleted_at", null);

      const { data: orderItems, error } = await query;

      if (error) {
        console.error("Error fetching dish summary:", error);
        return { success: false, error: `Failed to fetch dish summary: ${error.message}` };
      }

      if (!orderItems) {
        return { success: true, dishSummary: [] };
      }

      // Filter out items from deleted orders and apply date/payment filters
      const dishMap = new Map<string, { quantity: number; totalRevenue: number }>();

      orderItems.forEach((item: RawOrderItemForSummary & { orders?: { deleted_at?: string | null; order_date?: string | null; is_paid?: boolean | null } }) => {
        // Skip items from deleted orders
        if (item.orders?.deleted_at) {
          return;
        }

        const orderDate = item.orders?.order_date;
        const isPaid = item.orders?.is_paid;

        // Apply date filtering
        if (filterOptions?.dateFilter && orderDate) {
          const itemDate = new Date(orderDate).toISOString().split('T')[0];
          if (itemDate !== filterOptions.dateFilter) {
            return;
          }
        } else if (filterOptions?.dateRange && orderDate) {
          const itemDate = new Date(orderDate).toISOString().split('T')[0];
          if (itemDate < filterOptions.dateRange.startDate || itemDate > filterOptions.dateRange.endDate) {
            return;
          }
        }

        // Apply payment status filtering
        if (filterOptions?.paymentStatusFilter && filterOptions.paymentStatusFilter !== 'all') {
          const expectedPaidStatus = filterOptions.paymentStatusFilter === 'paid';
          if (isPaid !== expectedPaidStatus) {
            return;
          }
        }

        const dishName = item.products?.name;
        const quantity = item.quantity;
        const price = item.products?.price || 0;
        const revenue = quantity * price;

        if (dishName) {
          const existing = dishMap.get(dishName) || { quantity: 0, totalRevenue: 0 };
          dishMap.set(dishName, {
            quantity: existing.quantity + quantity,
            totalRevenue: existing.totalRevenue + revenue,
          });
        }
      });

      // Convert map to array and sort by quantity
      const dishSummary: DishSummaryData[] = Array.from(dishMap.entries())
        .map(([dishName, data]) => ({
          dishName,
          quantity: data.quantity,
          totalRevenue: data.totalRevenue,
        }))
        .sort((a, b) => b.quantity - a.quantity);

      return { success: true, dishSummary };
    } catch (error) {
      console.error("Unexpected error in getDishSummary:", error);
      return { success: false, error: "Unexpected error occurred while fetching dish summary" };
    }
  },

  /**
   * Updates payment status for multiple orders
   */
  async updatePaymentStatus(orderIds: string[], isPaid: boolean) {
    try {
      // Validate input
      if (!Array.isArray(orderIds) || orderIds.length === 0) {
        return { success: false, error: "No order IDs provided" };
      }

      // Filter out any invalid IDs
      const validOrderIds = orderIds.filter(id => id && typeof id === 'string' && id.trim().length > 0);

      if (validOrderIds.length === 0) {
        return { success: false, error: "No valid order IDs provided" };
      }

      const { error } = await supabase
        .from("orders")
        .update({
          is_paid: isPaid,
          updated_at: new Date().toISOString()
        })
        .in("id", validOrderIds)
        .is("deleted_at", null);

      if (error) {
        console.error("Error updating payment status:", error);
        return { success: false, error: `Failed to update payment status: ${error.message}` };
      }

      return { success: true };
    } catch (error) {
      console.error("Unexpected error in updatePaymentStatus:", error);
      return { success: false, error: "Unexpected error occurred while updating payment status" };
    }
  },
};
