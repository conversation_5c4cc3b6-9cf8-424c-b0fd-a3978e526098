import { CreditCard, CheckCircle, XCircle, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { UseOrderFiltersReturn, PaymentStatusFilter as PaymentStatusFilterType } from "@/hooks/useOrderFilters";

interface PaymentStatusFilterProps {
  orderFilters: UseOrderFiltersReturn;
  className?: string;
}

export function PaymentStatusFilter({ orderFilters, className }: PaymentStatusFilterProps) {
  const filterOptions: { 
    value: PaymentStatusFilterType; 
    label: string; 
    icon: React.ComponentType<{ className?: string }>; 
    color: string;
  }[] = [
    {
      value: 'all',
      label: 'Tất cả',
      icon: Filter,
      color: 'bg-gray-100 text-gray-800 border-gray-200',
    },
    {
      value: 'paid',
      label: 'Đã thanh toán',
      icon: CheckCircle,
      color: 'bg-green-100 text-green-800 border-green-200',
    },
    {
      value: 'unpaid',
      label: 'Chưa thanh toán',
      icon: XCircle,
      color: 'bg-red-100 text-red-800 border-red-200',
    },
  ];

  return (
    <div className={cn("flex flex-col sm:flex-row gap-3 items-start sm:items-center", className)}>
      {/* Filter Label */}
      <div className="flex items-center gap-2">
        <CreditCard className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium text-muted-foreground">Thanh toán:</span>
      </div>

      {/* Filter Buttons */}
      <div className="flex items-center gap-2">
        {filterOptions.map((option) => {
          const isActive = orderFilters.isFilterActive(option.value);
          const Icon = option.icon;
          
          return (
            <Button
              key={option.value}
              variant={isActive ? 'default' : 'outline'}
              size="sm"
              onClick={() => orderFilters.setPaymentStatusFilter(option.value)}
              className={cn(
                "text-xs gap-1.5 transition-all duration-200",
                isActive && "shadow-sm"
              )}
            >
              <Icon className="h-3 w-3" />
              {option.label}
            </Button>
          );
        })}
      </div>

      {/* Active Filter Badge */}
      {orderFilters.activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            {orderFilters.activeFiltersCount} bộ lọc
          </Badge>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={orderFilters.resetFilters}
            className="text-xs text-muted-foreground hover:text-foreground h-6 px-2"
            title="Xóa tất cả bộ lọc"
          >
            Xóa
          </Button>
        </div>
      )}
    </div>
  );
}
