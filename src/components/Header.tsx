import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { UtensilsCross<PERSON>, BarChart3, ClipboardList, Menu, X, Settings, Info, LogIn, User, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ProjectInfoModal } from "@/components/ProjectInfoModal";
import { LoginModal } from "@/components/LoginModal";
import { useAuth } from "@/hooks/useAuth";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProjectInfoOpen, setIsProjectInfoOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const location = useLocation();
  const { user, isAuthenticated, logout } = useAuth();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className="bg-gradient-primary shadow-elegant sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 p-2 rounded-lg">
              <UtensilsCrossed className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-white text-xl font-bold hidden sm:block">
              Đặt Cơm Công Ty
            </h1>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            <nav className="flex space-x-2">
              <Button
                variant={isActive('/orders') ? 'secondary' : 'ghost'}
                asChild
                className={isActive('/orders')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }
              >
                <Link to="/orders">
                  <UtensilsCrossed className="h-4 w-4 mr-2" />
                  Đặt Cơm
                </Link>
              </Button>

              <Button
                variant={isActive('/order-manage') ? 'secondary' : 'ghost'}
                asChild
                className={isActive('/order-manage')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }
              >
                <Link to="/order-manage">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Quản lý đơn đặt
                </Link>
              </Button>

              <Button
                variant={isActive('/statistics') ? 'secondary' : 'ghost'}
                asChild
                className={isActive('/statistics')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }
              >
                <Link to="/statistics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Thống Kê
                </Link>
              </Button>
            </nav>

            {/* Authentication Button */}
            {isAuthenticated ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-white/80 hover:bg-white/10 hover:text-white"
                  >
                    <User className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem disabled>
                    <User className="h-4 w-4 mr-2" />
                    {user?.name}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Đăng xuất
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsLoginModalOpen(true)}
                className="text-white/80 hover:bg-white/10 hover:text-white"
              >
                <LogIn className="h-4 w-4" />
              </Button>
            )}

            {/* Settings Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white/80 hover:bg-white/10 hover:text-white"
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem onClick={() => setIsProjectInfoOpen(true)}>
                  <Info className="h-4 w-4 mr-2" />
                  Project Information
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMenu}
            className="md:hidden text-white hover:bg-white/10"
          >
            {isMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-white/20 py-4 animate-fade-in">
            <nav className="flex flex-col space-y-2">
              <Button
                variant={isActive('/orders') ? 'secondary' : 'ghost'}
                asChild
                className={`justify-start ${isActive('/orders')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/orders">
                  <UtensilsCrossed className="h-4 w-4 mr-2" />
                  Đặt Cơm
                </Link>
              </Button>
              <Button
                variant={isActive('/statistics') ? 'secondary' : 'ghost'}
                asChild
                className={`justify-start ${isActive('/statistics')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/statistics">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Thống Kê
                </Link>
              </Button>
              <Button
                variant={isActive('/order-manage') ? 'secondary' : 'ghost'}
                asChild
                className={`justify-start ${isActive('/order-manage')
                  ? 'bg-white/20 text-white hover:bg-white/30'
                  : 'text-white/80 hover:bg-white/10 hover:text-white'
                }`}
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/order-manage">
                  <ClipboardList className="h-4 w-4 mr-2" />
                  Quản lý đơn đặt
                </Link>
              </Button>

              {/* Mobile Authentication Button */}
              {isAuthenticated ? (
                <>
                  <Button
                    variant="ghost"
                    className="justify-start text-white/80 hover:bg-white/10 hover:text-white"
                    disabled
                  >
                    <User className="h-4 w-4 mr-2" />
                    {user?.name}
                  </Button>
                  <Button
                    variant="ghost"
                    className="justify-start text-white/80 hover:bg-white/10 hover:text-white"
                    onClick={() => {
                      logout();
                      setIsMenuOpen(false);
                    }}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Đăng xuất
                  </Button>
                </>
              ) : (
                <Button
                  variant="ghost"
                  className="justify-start text-white/80 hover:bg-white/10 hover:text-white"
                  onClick={() => {
                    setIsLoginModalOpen(true);
                    setIsMenuOpen(false);
                  }}
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  Đăng nhập
                </Button>
              )}

              {/* Mobile Settings Button */}
              <Button
                variant="ghost"
                className="justify-start text-white/80 hover:bg-white/10 hover:text-white"
                onClick={() => {
                  setIsProjectInfoOpen(true);
                  setIsMenuOpen(false);
                }}
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </nav>
          </div>
        )}
      </div>

      {/* Project Information Modal */}
      <ProjectInfoModal
        open={isProjectInfoOpen}
        onOpenChange={setIsProjectInfoOpen}
      />

      {/* Login Modal */}
      <LoginModal
        open={isLoginModalOpen}
        onOpenChange={setIsLoginModalOpen}
      />
    </header>
  );
}