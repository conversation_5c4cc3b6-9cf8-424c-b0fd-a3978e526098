import { ArrowUpDown, ArrowUp, ArrowDown, SortAsc } from "lucide-react";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { UseOrderFiltersReturn, OrderSortOption } from "@/hooks/useOrderFilters";

interface OrderSortControlsProps {
  orderFilters: UseOrderFiltersReturn;
  className?: string;
}

export function OrderSortControls({ orderFilters, className }: OrderSortControlsProps) {
  const sortOptions: { value: OrderSortOption; label: string }[] = [
    { value: 'recentDate', label: 'Ngày gần nhất' },
    { value: 'totalAmount', label: 'Tổng tiền' },
    { value: 'orderCount', label: 'Số đơn hàng' },
    { value: 'userName', label: 'Tên người dùng' },
    { value: 'paymentStatus', label: 'Trạng thái thanh toán' },
  ];

  const currentSortLabel = orderFilters.getSortLabel(orderFilters.sortOption);
  const SortDirectionIcon = orderFilters.sortDirection === 'asc' ? ArrowUp : ArrowDown;

  return (
    <div className={cn("flex items-center gap-3", className)}>
      {/* Sort Label */}
      <div className="flex items-center gap-2">
        <SortAsc className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium text-muted-foreground">Sắp xếp:</span>
      </div>

      {/* Sort Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="gap-2 min-w-[140px] justify-between"
          >
            <span className="text-xs">{currentSortLabel}</span>
            <ArrowUpDown className="h-3 w-3 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-48">
          {sortOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() => orderFilters.setSortOption(option.value)}
              className={cn(
                "text-xs cursor-pointer",
                orderFilters.sortOption === option.value && "bg-accent"
              )}
            >
              {option.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Sort Direction Toggle */}
      <Button
        variant="outline"
        size="sm"
        onClick={orderFilters.toggleSortDirection}
        className="gap-1.5 px-3"
        title={`Sắp xếp ${orderFilters.sortDirection === 'asc' ? 'tăng dần' : 'giảm dần'}`}
      >
        <SortDirectionIcon className="h-3 w-3" />
        <span className="text-xs">
          {orderFilters.sortDirection === 'asc' ? 'Tăng' : 'Giảm'}
        </span>
      </Button>

      {/* Current Sort Display */}
      <Badge variant="outline" className="text-xs gap-1">
        <SortDirectionIcon className="h-2.5 w-2.5" />
        {currentSortLabel}
      </Badge>
    </div>
  );
}
