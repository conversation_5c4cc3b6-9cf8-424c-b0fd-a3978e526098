import { useState } from "react";
import { Calendar as CalendarIcon, ChevronDown, RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { UseDateFilterReturn } from "@/hooks/useDateFilter";

interface DateFilterControlsProps {
  dateFilter: UseDateFilterReturn;
  className?: string;
}

export function DateFilterControls({ dateFilter, className }: DateFilterControlsProps) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      dateFilter.setSelectedDate(date);
      setIsCalendarOpen(false);
    }
  };

  const getDateDisplayText = () => {
    switch (dateFilter.dateFilterType) {
      case 'today':
        return 'Hôm nay';
      case 'yesterday':
        return 'Hôm qua';
      case 'thisWeek':
        return 'Tuần này';
      case 'custom':
        return dateFilter.formattedDate;
      default:
        return dateFilter.formattedDate;
    }
  };

  const getDateBadgeVariant = () => {
    switch (dateFilter.dateFilterType) {
      case 'today':
        return 'default';
      case 'yesterday':
        return 'secondary';
      case 'thisWeek':
        return 'outline';
      case 'custom':
        return 'destructive';
      default:
        return 'default';
    }
  };

  return (
    <div className={cn("flex flex-col sm:flex-row gap-4 items-start sm:items-center", className)}>
      {/* Date Display and Calendar Picker */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">Ngày:</span>
        </div>
        
        <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal min-w-[200px]",
                !dateFilter.selectedDate && "text-muted-foreground"
              )}
            >
              <div className="flex items-center gap-2 flex-1">
                <Badge variant={getDateBadgeVariant()} className="text-xs">
                  {getDateDisplayText()}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  ({dateFilter.formattedDate})
                </span>
              </div>
              <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={dateFilter.selectedDate}
              onSelect={handleDateSelect}
              disabled={(date) =>
                date > new Date() || date < new Date("1900-01-01")
              }
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      {/* Quick Navigation Buttons */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          <Button
            variant={dateFilter.dateFilterType === 'today' ? 'default' : 'outline'}
            size="sm"
            onClick={dateFilter.setToday}
            className="text-xs"
          >
            Hôm nay
          </Button>
          
          <Button
            variant={dateFilter.dateFilterType === 'yesterday' ? 'default' : 'outline'}
            size="sm"
            onClick={dateFilter.setYesterday}
            className="text-xs"
          >
            Hôm qua
          </Button>
          
          <Button
            variant={dateFilter.dateFilterType === 'thisWeek' ? 'default' : 'outline'}
            size="sm"
            onClick={dateFilter.setThisWeek}
            className="text-xs"
          >
            Tuần này
          </Button>
        </div>

        {/* Reset Button */}
        {dateFilter.dateFilterType !== 'today' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={dateFilter.resetToToday}
            className="text-xs text-muted-foreground hover:text-foreground"
            title="Về hôm nay"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}
