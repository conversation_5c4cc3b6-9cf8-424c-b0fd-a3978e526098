import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Clock } from "lucide-react";

interface PaymentStatusBadgeProps {
  isPaid: boolean;
  className?: string;
}

export function PaymentStatusBadge({ isPaid, className = "" }: PaymentStatusBadgeProps) {
  if (isPaid) {
    return (
      <Badge 
        variant="secondary" 
        className={`bg-green-50 text-green-700 border-green-200 ${className}`}
      >
        <CheckCircle className="h-3 w-3 mr-1" />
        Đã thanh toán
      </Badge>
    );
  } else {
    return (
      <Badge 
        variant="secondary" 
        className={`bg-red-50 text-red-700 border-red-200 ${className}`}
      >
        <XCircle className="h-3 w-3 mr-1" />
        Chưa thanh toán
      </Badge>
    );
  }
}

interface PaymentStatusGroupBadgeProps {
  status: 'all_paid' | 'all_unpaid' | 'mixed';
  paidCount?: number;
  totalCount?: number;
  className?: string;
}

export function PaymentStatusGroupBadge({ 
  status, 
  paidCount = 0, 
  totalCount = 0, 
  className = "" 
}: PaymentStatusGroupBadgeProps) {
  if (status === 'all_paid') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <CheckCircle className="h-4 w-4 text-green-600" />
        <span className="text-green-700 font-medium text-sm">Đã thanh toán</span>
      </div>
    );
  } else if (status === 'all_unpaid') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <XCircle className="h-4 w-4 text-red-600" />
        <span className="text-red-700 font-medium text-sm">Chưa thanh toán</span>
      </div>
    );
  } else {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Clock className="h-4 w-4 text-orange-600" />
        <span className="text-orange-700 font-medium text-sm">
          {paidCount}/{totalCount} đã thanh toán 
        </span>
      </div>
    );
  }
}
