import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2, LogIn, X, UserPlus } from "lucide-react";
import { authService } from "@/services/authService";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "@/hooks/use-toast";

// Form validation schema
const loginSchema = z.object({
  name: z
    .string()
    .min(1, "Tên không được để trống")
    .max(100, "Tên không được quá 100 ký tự")
    .trim(),
  password: z
    .string()
    .min(1, "Mật khẩu không được để trống")
    .min(4, "Mật khẩu phải có ít nhất 4 ký tự")
    .max(50, "Mật khẩu không được quá 50 ký tự"),
});

type LoginFormData = z.infer<typeof loginSchema>;

interface LoginModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function LoginModal({ open, onOpenChange }: LoginModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isRegisterMode, setIsRegisterMode] = useState(false);
  const { login } = useAuth();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      name: "",
      password: "",
    },
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);

    try {
      // Ensure data matches LoginCredentials interface
      const credentials = {
        name: data.name,
        password: data.password,
      };

      const result = isRegisterMode
        ? await authService.register(credentials)
        : await authService.login(credentials);

      if (result.success && result.user) {
        login(result.user);
        toast({
          title: isRegisterMode ? "Đăng ký thành công" : "Đăng nhập thành công",
          description: `Chào mừng ${result.user.name}!`,
        });

        onOpenChange(false);
        form.reset();
        setIsRegisterMode(false);

        // 👇 Force hard reload
        window.location.reload();
      } else {
        toast({
          variant: "destructive",
          title: isRegisterMode ? "Lỗi đăng ký" : "Lỗi đăng nhập",
          description: result.error || "Đã xảy ra lỗi không xác định",
        });
      }
    } catch (error) {
      console.error("Authentication error:", error);
      toast({
        variant: "destructive",
        title: "Lỗi hệ thống",
        description: "Đã xảy ra lỗi không mong muốn. Vui lòng thử lại.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    onOpenChange(false);
    form.reset();
    setIsRegisterMode(false);
  };

  const toggleMode = () => {
    setIsRegisterMode(!isRegisterMode);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md bg-gradient-card">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            {isRegisterMode ? (
              <>
                <UserPlus className="h-5 w-5 text-primary" />
                Đăng Ký Tài Khoản
              </>
            ) : (
              <>
                <LogIn className="h-5 w-5 text-primary" />
                Đăng Nhập
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <Card className="border-0 shadow-none bg-transparent">
          <CardContent className="p-0 space-y-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tên người dùng</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Nhập tên của bạn..."
                          {...field}
                          disabled={isLoading}
                          className="bg-white/50 border-white/20 focus:border-primary/50"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mật khẩu</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="Nhập mật khẩu..."
                          {...field}
                          disabled={isLoading}
                          className="bg-white/50 border-white/20 focus:border-primary/50"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex flex-col gap-3 pt-2">
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-primary hover:bg-primary/90 text-white"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        {isRegisterMode ? "Đang đăng ký..." : "Đang đăng nhập..."}
                      </>
                    ) : (
                      <>
                        {isRegisterMode ? (
                          <>
                            <UserPlus className="h-4 w-4 mr-2" />
                            Đăng Ký
                          </>
                        ) : (
                          <>
                            <LogIn className="h-4 w-4 mr-2" />
                            Đăng Nhập
                          </>
                        )}
                      </>
                    )}
                  </Button>

                  <Button
                    type="button"
                    variant="ghost"
                    onClick={toggleMode}
                    disabled={isLoading}
                    className="w-full text-muted-foreground hover:text-foreground"
                  >
                    {isRegisterMode 
                      ? "Đã có tài khoản? Đăng nhập" 
                      : "Chưa có tài khoản? Đăng ký"}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isLoading}
                    className="w-full border-white/20 hover:bg-white/10"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Hủy
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
