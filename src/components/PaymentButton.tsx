import { Button } from "@/components/ui/button";
import { CreditCard, CheckCircle } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";

interface AdminPaymentButtonProps {
  isPaid: boolean;
  onMarkAsPaid: () => void;
  disabled?: boolean;
  size?: "sm" | "default" | "lg" | "icon";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  className?: string;
}

export function PaymentButton({
  isPaid,
  onMarkAsPaid,
  disabled = false,
  size = "sm",
  variant = "outline",
  className = ""
}: AdminPaymentButtonProps) {
  const { user } = useAuth();

  // Don't show if already paid
  if (isPaid) {
    return (
      <Button
        variant="ghost"
        size={size}
        disabled
        className={`text-green-600 cursor-not-allowed ${className}`}
        title="Đã thanh toán"
      >
        <CheckCircle className="h-4 w-4" />
      </Button>
    );
  }

  if (user && user.role === 'admin') {
    return (
      <Button
        variant={variant}
        size={size}
        onClick={onMarkAsPaid}
        disabled={disabled}
        className={`text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200 ${className}`}
        title="Đánh dấu đã thanh toán"
      >
        <CreditCard className="h-4 w-4" />
      </Button>
  );
}
}
