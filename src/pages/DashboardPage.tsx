import { StatCard } from "@/components/StatCard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useDashboardData } from "@/hooks/useDashboardData";
import {
  DollarSign,
  Users,
  CreditCard,
  TrendingUp,
  TrendingDown,
  Trophy,
  Calendar,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";

export function DashboardPage() {
  const { data: dashboardData, isLoading, error, refetch } = useDashboardData(true);

  const formatPrice = (price: number) => `¥${price.toLocaleString()}`;

  const todayDate = new Date().toLocaleDateString('vi-VN', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const { stats, categoryData, weeklyData } = dashboardData;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-warm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-primary" />
              <p className="text-muted-foreground">Đang tải dữ liệu dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-warm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <Card className="bg-gradient-card shadow-card max-w-md">
              <CardContent className="text-center py-8">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-700 mb-2">
                  Lỗi tải dữ liệu
                </h3>
                <p className="text-muted-foreground mb-4">{error}</p>
                <button
                  onClick={refetch}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  <RefreshCw className="h-4 w-4" />
                  Thử lại
                </button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-warm">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            📊 Dashboard Thống Kê
          </h1>
          <div className="flex items-center justify-center gap-4 text-muted-foreground">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span>{todayDate}</span>
            </div>
            <button
              onClick={refetch}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-muted hover:bg-muted/80 rounded-md transition-colors"
              title="Làm mới dữ liệu"
            >
              <RefreshCw className="h-3 w-3" />
              Làm mới
            </button>
          </div>
        </div>

        {/* Main Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="💰 Doanh Thu Hôm Nay"
            value={formatPrice(stats.totalRevenue)}
            description="Tổng tiền các đơn hàng"
            icon={DollarSign}
            gradient="bg-gradient-to-br from-green-50 to-emerald-100"
          />
          
          <StatCard
            title="👥 Số Người Đặt Cơm"
            value={stats.totalOrders}
            description="Đơn hàng hôm nay"
            icon={Users}
            gradient="bg-gradient-to-br from-blue-50 to-indigo-100"
          />
          
          <StatCard
            title="💸 Tổng Nợ Hiện Tại"
            value={formatPrice(stats.totalDebt)}
            description="Chưa thanh toán"
            icon={CreditCard}
            gradient="bg-gradient-to-br from-red-50 to-pink-100"
          />
          
          <StatCard
            title="✅ Tỉ Lệ Thanh Toán"
            value={`${stats.totalOrders > 0 ? Math.round((stats.paidOrders / stats.totalOrders) * 100) : 0}%`}
            description={`${stats.paidOrders}/${stats.totalOrders} đã trả`}
            icon={stats.paidOrders > stats.unpaidOrders ? TrendingUp : TrendingDown}
            gradient="bg-gradient-to-br from-purple-50 to-violet-100"
          />
        </div>

        {/* Payment Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="bg-gradient-to-br from-green-50 to-emerald-100 shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700">
                <div className="bg-green-200 p-2 rounded-lg">
                  <TrendingUp className="h-5 w-5" />
                </div>
                Đã Thanh Toán
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-800 mb-2">
                {stats.paidOrders}
              </div>
              <p className="text-green-600 text-sm">
                người đã trả tiền hôm nay
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-red-100 shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <div className="bg-red-200 p-2 rounded-lg">
                  <TrendingDown className="h-5 w-5" />
                </div>
                Chưa Thanh Toán
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-800 mb-2">
                {stats.unpaidOrders}
              </div>
              <p className="text-red-600 text-sm">
                người chưa trả tiền
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pie Chart */}
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="bg-blue-200 p-2 rounded-lg">
                  <Trophy className="h-5 w-5 text-blue-700" />
                </div>
                🥘 Tỷ Lệ Loại Món Ăn Hôm Nay
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {categoryData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <p>Chưa có dữ liệu loại món ăn</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Bar Chart */}
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="bg-green-200 p-2 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-700" />
                </div>
                📊 Số Đơn Đặt Theo Ngày (7 ngày qua)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {weeklyData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={weeklyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip
                        formatter={(value, name) => {
                          if( name === 'Số đơn') {
                            return `${value}`;
                          }
                          return `$${value}`;
                        }}
                      />
                      <Legend />
                      <Bar dataKey="orders" fill="hsl(var(--primary))" name="Số đơn" />
                      <Bar dataKey="revenue" fill="hsl(var(--secondary))" name="Doanh thu" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    <p>Chưa có dữ liệu đơn hàng theo ngày</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Items */}
        {stats.topItems.length > 0 && (
          <Card className="bg-gradient-card shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="bg-yellow-200 p-2 rounded-lg">
                  <Trophy className="h-5 w-5 text-yellow-700" />
                </div>
                🏆 Top 5 Món Ăn Được Đặt Nhiều Nhất Hôm Nay
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stats.topItems.map((item, index) => (
                  <div key={item.product.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge
                        variant="secondary"
                        className={`
                          ${index === 0 ? 'bg-yellow-200 text-yellow-800' : ''}
                          ${index === 1 ? 'bg-gray-200 text-gray-800' : ''}
                          ${index === 2 ? 'bg-orange-200 text-orange-800' : ''}
                        `}
                      >
                        #{index + 1}
                      </Badge>
                      <span className="font-medium">{item.product.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {item.product.category === 'main' ? 'Món chính' : 'Món phụ'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-muted-foreground">
                        {item.quantity} lần
                      </span>
                      <span className="font-semibold text-primary">
                        {formatPrice(item.product.price)}
                      </span>
                      <span className="text-xs text-green-600 font-medium">
                        {formatPrice(item.revenue)} tổng
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {stats.totalOrders === 0 && (
          <Card className="bg-gradient-card shadow-card">
            <CardContent className="text-center py-12">
              <div className="text-6xl mb-4">🍱</div>
              <h3 className="text-xl font-semibold text-muted-foreground mb-2">
                Chưa có đơn hàng nào hôm nay
              </h3>
              <p className="text-muted-foreground">
                Hãy bắt đầu đặt cơm để xem thống kê!
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}