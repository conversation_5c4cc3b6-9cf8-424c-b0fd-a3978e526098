import { useState, useMemo } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { CheckCircle, Users, ShoppingBag, RefreshCw, AlertCircle, History, TrendingUp, Calendar, Filter } from "lucide-react";
import { useFilteredOrders } from "@/hooks/useFilteredOrders";
import { OrderHistoryDialog } from "@/components/OrderHistoryDialog";
import { PaymentStatusGroupBadge } from "@/components/PaymentStatusBadge";
import { PaymentConfirmationDialog } from "@/components/PaymentConfirmationDialog";
import { DateFilterControls } from "@/components/DateFilterControls";
import { PaymentStatusFilter } from "@/components/PaymentStatusFilter";
import { OrderSortControls } from "@/components/OrderSortControls";
import { supabaseOrderService } from "@/services/supabaseOrderService";

import { useToast } from "@/hooks/use-toast";
import { PaymentButton } from "@/components/PaymentButton";

type GroupedOrderData = {
  userName: string;
  totalAmount: number;
  allDishes: string[];
  orderIds: string[];
  orderCount: number;
  paidOrdersCount: number;
  unpaidOrdersCount: number;
  mostRecentOrderDate: string;
  isPaidStatus: 'all_paid' | 'all_unpaid' | 'mixed';
}

const chipColors = [
  "bg-blue-100 text-blue-800",
  "bg-green-100 text-green-800",
  "bg-purple-100 text-purple-800",
  "bg-orange-100 text-orange-800",
  "bg-pink-100 text-pink-800",
  "bg-yellow-100 text-yellow-800",
  "bg-indigo-100 text-indigo-800",
  "bg-red-100 text-red-800",
];

export function OrderManagementPage() {
  const {
    orders,
    dishSummary,
    isLoading,
    error,
    totalOrders,
    refetch,
    clearError,
    dateFilter,
    orderFilters,
    currentFilterOptions
  } = useFilteredOrders();
  console.log("%c 👩‍🚒: orders ", "font-size:16px;background-color:#d54fe1;color:white;", orders)
  console.log("%c 🔍: currentFilterOptions ", "font-size:16px;background-color:#3498db;color:white;", currentFilterOptions)
  const { toast } = useToast();
  
  // History dialog state
  const [isHistoryDialogOpen, setIsHistoryDialogOpen] = useState(false);
  const [selectedUserName, setSelectedUserName] = useState<string>("");

  // Payment confirmation dialog state
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedPaymentOrder, setSelectedPaymentOrder] = useState<{
    orderIds: string[];
    customerName: string;
    totalAmount: number;
    orderCount?: number;
  } | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const formatPrice = (price: number) => `¥${price.toLocaleString()}`;

  const getChipColor = (index: number) => {
    return chipColors[index % chipColors.length];
  };

  const handleViewHistory = (userName: string) => {
    setSelectedUserName(userName);
    setIsHistoryDialogOpen(true);
  };

  const handleMarkUserOrdersAsPaid = (userName: string, totalAmount: number, orderCount: number, orderIds: string[]) => {
    setSelectedPaymentOrder({
      orderIds: orderIds,
      customerName: userName,
      totalAmount,
      orderCount
    });
    setIsPaymentDialogOpen(true);
  };

  const handleConfirmPayment = async () => {
    if (!selectedPaymentOrder) return;

    try {
      setIsProcessingPayment(true);

      // Update payment status for all order IDs
      const result = await supabaseOrderService.updatePaymentStatus(
        selectedPaymentOrder.orderIds,
        true
      );

      if (result.success) {
        toast({
          title: "Thanh toán thành công",
          description: `Đã đánh dấu tất cả đơn hàng của ${selectedPaymentOrder.customerName} là đã thanh toán.`,
          variant: "default",
        });
        
        // Refresh data to show updated payment status
        await refetch();
      } else {
        toast({
          title: "Lỗi thanh toán",
          description: result.error || "Không thể cập nhật trạng thái thanh toán",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Payment confirmation error:", error);
      toast({
        title: "Lỗi hệ thống",
        description: "Đã xảy ra lỗi khi xử lý thanh toán",
        variant: "destructive",
      });
    } finally {
      setIsProcessingPayment(false);
      setSelectedPaymentOrder(null);
      setIsPaymentDialogOpen(false);
    }
  };

  // Group orders by user name with sorting based on current filter settings
  const groupedOrders = useMemo(() => {
    const grouped = new Map<string, GroupedOrderData>();

    orders.forEach(order => {
      const existing = grouped.get(order.userName);

      if (existing) {
        // Update existing group
        existing.totalAmount += order.totalAmount;
        existing.allDishes.push(...order.dishes);
        existing.orderIds.push(order.id);
        existing.orderCount += 1;

        if (order.isPaid) {
          existing.paidOrdersCount += 1;
        } else {
          existing.unpaidOrdersCount += 1;
        }

        // Update most recent order date
        if (new Date(order.orderDate) > new Date(existing.mostRecentOrderDate)) {
          existing.mostRecentOrderDate = order.orderDate;
        }

        // Update payment status
        if (existing.paidOrdersCount > 0 && existing.unpaidOrdersCount > 0) {
          existing.isPaidStatus = 'mixed';
        } else if (existing.paidOrdersCount > 0) {
          existing.isPaidStatus = 'all_paid';
        } else {
          existing.isPaidStatus = 'all_unpaid';
        }
      } else {
        // Create new group
        grouped.set(order.userName, {
          userName: order.userName,
          totalAmount: order.totalAmount,
          allDishes: [...order.dishes],
          orderIds: [order.id],
          orderCount: 1,
          paidOrdersCount: order.isPaid ? 1 : 0,
          unpaidOrdersCount: order.isPaid ? 0 : 1,
          mostRecentOrderDate: order.orderDate,
          isPaidStatus: order.isPaid ? 'all_paid' : 'all_unpaid',
        });
      }
    });

    // Sort based on current sort settings
    const sortedGroups = Array.from(grouped.values()).sort((a, b) => {
      const { sortOption, sortDirection } = orderFilters;
      let comparison = 0;

      switch (sortOption) {
        case 'totalAmount':
          comparison = a.totalAmount - b.totalAmount;
          break;
        case 'orderCount':
          comparison = a.orderCount - b.orderCount;
          break;
        case 'userName':
          comparison = a.userName.localeCompare(b.userName);
          break;
        case 'paymentStatus': {
          // Sort by payment status: all_paid > mixed > all_unpaid
          const statusOrder = { 'all_paid': 2, 'mixed': 1, 'all_unpaid': 0 };
          comparison = statusOrder[a.isPaidStatus] - statusOrder[b.isPaidStatus];
          break;
        }
        case 'recentDate':
        default:
          comparison = new Date(a.mostRecentOrderDate).getTime() - new Date(b.mostRecentOrderDate).getTime();
          break;
      }

      return sortDirection === 'asc' ? comparison : -comparison;
    });

    return sortedGroups;
  }, [orders, orderFilters]);
    console.log("%c 🇸🇳: groupedOrders -> groupedOrders ", "font-size:16px;background-color:#54f681;color:black;", groupedOrders)

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-warm">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground">Đang tải dữ liệu đơn hàng...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-warm">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-foreground">
            📋 Quản Lý Đơn Đặt
          </h1>
          <div className="flex items-center justify-center gap-2 text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Đang xem đơn hàng:</span>
            <Badge variant="outline" className="font-medium">
              {dateFilter.dateFilterType === 'today' && 'Hôm nay'}
              {dateFilter.dateFilterType === 'yesterday' && 'Hôm qua'}
              {dateFilter.dateFilterType === 'thisWeek' && 'Tuần này'}
              {dateFilter.dateFilterType === 'custom' && dateFilter.formattedDate}
              {dateFilter.dateFilterType !== 'custom' && ` (${dateFilter.formattedDate})`}
            </Badge>
          </div>
          <div className="flex justify-center">
            <Button
              onClick={() => refetch()}
              variant="outline"
              size="sm"
              className="gap-2"
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Làm mới dữ liệu
            </Button>
          </div>
        </div>

        {/* Filter Controls */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Bộ lọc và sắp xếp
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Date Filter Controls */}
            <DateFilterControls dateFilter={dateFilter} />

            {/* Payment Status and Sort Controls */}
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
              <PaymentStatusFilter orderFilters={orderFilters} />
              <OrderSortControls orderFilters={orderFilters} />
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="bg-red-50 border-red-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <div className="flex-1">
                  <p className="text-red-800 font-medium">Lỗi tải dữ liệu</p>
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
                <Button
                  onClick={clearError}
                  variant="ghost"
                  size="sm"
                  className="text-red-600 hover:text-red-800"
                >
                  Đóng
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="bg-blue-200 p-3 rounded-lg">
                  <Users className="h-6 w-6 text-blue-700" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-600">Người dùng</p>
                  <p className="text-2xl font-bold text-blue-800">{groupedOrders.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-emerald-100 shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="bg-green-200 p-3 rounded-lg">
                  <ShoppingBag className="h-6 w-6 text-green-700" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-600">Tổng đơn hàng</p>
                  <p className="text-2xl font-bold text-green-800">{totalOrders}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-violet-100 shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="bg-purple-200 p-3 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-700" />
                </div>
                <div>
                  <p className="text-sm font-medium text-purple-600">Tổng doanh thu</p>
                  <p className="text-2xl font-bold text-purple-800">
                    {formatPrice(groupedOrders.reduce((sum, order) => sum + order.totalAmount, 0))}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-amber-100 shadow-card">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="bg-orange-200 p-3 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-orange-700" />
                </div>
                <div>
                  <p className="text-sm font-medium text-orange-600">Đã thanh toán</p>
                  <p className="text-2xl font-bold text-orange-800">
                    {groupedOrders.reduce((sum, order) => sum + order.paidOrdersCount, 0)}/{totalOrders}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Grouped Orders Cards */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl font-semibold">👥 Đơn đặt theo người dùng</CardTitle>
              <Button
                onClick={() => handleViewHistory("")}
                variant="outline"
                size="sm"
                className="gap-2"
                title="Xem lịch sử tất cả người dùng"
              >
                <History className="h-4 w-4" />
                Lịch sử chung
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {groupedOrders.length === 0 ? (
              <div className="text-center py-12">
                <div className="mx-auto w-24 h-24 bg-muted/20 rounded-full flex items-center justify-center mb-4">
                  <ShoppingBag className="h-8 w-8 text-muted-foreground" />
                </div>
                <p className="text-muted-foreground text-lg">
                  Không có đơn hàng nào cho{' '}
                  {dateFilter.dateFilterType === 'today' && 'hôm nay'}
                  {dateFilter.dateFilterType === 'yesterday' && 'hôm qua'}
                  {dateFilter.dateFilterType === 'thisWeek' && 'tuần này'}
                  {dateFilter.dateFilterType === 'custom' && `ngày ${dateFilter.formattedDate}`}
                </p>
                <p className="text-muted-foreground/70 text-sm mt-1">
                  {orderFilters.paymentStatusFilter !== 'all'
                    ? `Không có đơn hàng ${orderFilters.getFilterLabel(orderFilters.paymentStatusFilter).toLowerCase()} cho ngày này`
                    : 'Thử chọn ngày khác hoặc kiểm tra lại bộ lọc'
                  }
                </p>
                <div className="mt-4 flex justify-center gap-2">
                  {dateFilter.dateFilterType !== 'today' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={dateFilter.setToday}
                      className="gap-2"
                    >
                      <Calendar className="h-4 w-4" />
                      Xem hôm nay
                    </Button>
                  )}
                  {orderFilters.activeFiltersCount > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={orderFilters.resetFilters}
                      className="gap-2"
                    >
                      <Filter className="h-4 w-4" />
                      Xóa bộ lọc
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {groupedOrders.map((groupedOrder) => (
                  <Card
                    key={groupedOrder.userName}
                    className="bg-gradient-to-br from-white to-gray-50/50 border-2 border-gray-100 hover:border-primary/20 hover:shadow-lg transition-all duration-300 group"
                    // onClick={() => handleViewHistory(groupedOrder.userName)}
                  >
                    <CardContent className="p-6">
                      {/* User Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-primary/10 to-primary/20 rounded-full flex items-center justify-center">
                            <Users className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg text-foreground ">
                              {groupedOrder.userName}
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              {groupedOrder.orderCount} đơn hàng
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <PaymentButton
                            isPaid={groupedOrder.isPaidStatus === 'all_paid'}
                            onMarkAsPaid={() => handleMarkUserOrdersAsPaid(
                              groupedOrder.userName,
                              groupedOrder.totalAmount,
                              groupedOrder.orderCount,
                              groupedOrder.orderIds
                            )}
                            disabled={isProcessingPayment}
                          />
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewHistory(groupedOrder.userName);
                            }}
                          >
                            <History className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Total Amount */}
                      <div className="mb-4">
                        <div className="flex items-center gap-2 mb-1">
                          <TrendingUp className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-muted-foreground">Tổng tiền</span>
                        </div>
                        <p className="">
                          {formatPrice(groupedOrder.totalAmount)}
                        </p>
                      </div>

                      {/* Payment Status: "Đã thanh toán hết" hoặc "Chưa thanh toán" */}
                      <div className="mb-4">
                        <PaymentStatusGroupBadge 
                          status={groupedOrder.isPaidStatus}
                          paidCount={groupedOrder.paidOrdersCount}
                          totalCount={groupedOrder.orderCount}
                        />
                      </div>

                      {/* Dishes */}
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm font-medium text-muted-foreground">Món đã đặt</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {groupedOrder.allDishes.length === 0 ? (
                            <span className="text-muted-foreground text-sm">Không có món</span>
                          ) : (
                            // Show unique dishes with count
                            Array.from(new Set(groupedOrder.allDishes)).slice(0, 6).map((dish, index) => {
                              const count = groupedOrder.allDishes.filter(d => d === dish).length;
                              return (
                                <Badge
                                  key={index}
                                  variant="secondary"
                                  className={`text-xs ${getChipColor(index)} border-0`}
                                >
                                  {dish} {count > 1 && `(${count})`}
                                </Badge>
                              );
                            })
                          )}
                          {Array.from(new Set(groupedOrder.allDishes)).length > 6 && (
                            <Badge variant="outline" className="text-xs">
                              +{Array.from(new Set(groupedOrder.allDishes)).length - 6} khác
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Dish Summary Table */}
        <Card className="bg-gradient-card shadow-card">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">📊 Tổng hợp món đã đặt</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="font-semibold">Tên sản phẩm</TableHead>
                    <TableHead className="font-semibold">Số lượng</TableHead>
                    <TableHead className="font-semibold">Tổng tiền</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dishSummary.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-8 text-muted-foreground">
                        Chưa có dữ liệu món ăn
                      </TableCell>
                    </TableRow>
                  ) : (
                    dishSummary
                      .sort((a, b) => b.quantity - a.quantity)
                      .map((dish, index) => (
                        <TableRow key={index} className="hover:bg-muted/50">
                          <TableCell className="font-medium">{dish.dishName}</TableCell>
                          <TableCell>
                            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                              {dish.quantity}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-semibold text-primary">
                            {formatPrice(dish.totalRevenue)}
                          </TableCell>
                        </TableRow>
                      ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Order History Dialog */}
        <OrderHistoryDialog
          isOpen={isHistoryDialogOpen}
          onClose={() => setIsHistoryDialogOpen(false)}
          userName={selectedUserName}
          showAllUsers={!selectedUserName.trim()} // Show all users when no specific user is selected
        />

        {/* Payment Confirmation Dialog */}
        {selectedPaymentOrder && (
          <PaymentConfirmationDialog
            isOpen={isPaymentDialogOpen}
            onClose={() => {
              setIsPaymentDialogOpen(false);
              setSelectedPaymentOrder(null);
            }}
            onConfirm={handleConfirmPayment}
            orderInfo={selectedPaymentOrder}
            isProcessing={isProcessingPayment}
          />
        )}
      </div>
    </div>
  );
}