import { supabaseOrderService } from "@/services/supabaseOrderService";

/**
 * Test utility for verifying bulk payment status updates
 * This can be called from the browser console for testing
 */
export const testBulkPaymentUpdate = {
  /**
   * Test updating payment status for multiple orders
   */
  async testMultipleOrderUpdate() {
    console.log("🧪 Testing bulk payment status update...");
    
    try {
      // First, get all orders to find some unpaid ones
      const ordersResult = await supabaseOrderService.getAllOrders();
      
      if (!ordersResult.success || !ordersResult.orders) {
        console.error("❌ Failed to fetch orders:", ordersResult.error);
        return { success: false, error: ordersResult.error };
      }

      // Find some unpaid orders
      const unpaidOrders = ordersResult.orders.filter(order => !order.isPaid);
      
      if (unpaidOrders.length === 0) {
        console.log("ℹ️ No unpaid orders found to test with");
        return { success: true, message: "No unpaid orders to test" };
      }

      // Take first 2 unpaid orders for testing
      const testOrderIds = unpaidOrders.slice(0, 2).map(order => order.id);
      
      console.log(`📋 Testing with order IDs: ${testOrderIds.join(", ")}`);

      // Test the bulk update
      const updateResult = await supabaseOrderService.updatePaymentStatus(testOrderIds, true);
      
      if (updateResult.success) {
        console.log("✅ Bulk payment update successful!");
        
        // Verify the update by fetching the orders again
        const verifyResult = await supabaseOrderService.getAllOrders();
        if (verifyResult.success && verifyResult.orders) {
          const updatedOrders = verifyResult.orders.filter(order => testOrderIds.includes(order.id));
          const allPaid = updatedOrders.every(order => order.isPaid);
          
          if (allPaid) {
            console.log("✅ Verification successful: All test orders are now marked as paid");
            return { success: true, updatedOrderIds: testOrderIds };
          } else {
            console.error("❌ Verification failed: Some orders were not updated");
            return { success: false, error: "Verification failed" };
          }
        }
      } else {
        console.error("❌ Bulk payment update failed:", updateResult.error);
        return { success: false, error: updateResult.error };
      }
    } catch (error) {
      console.error("❌ Test failed with error:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test with invalid input
   */
  async testInvalidInput() {
    console.log("🧪 Testing bulk payment update with invalid input...");
    
    try {
      // Test with empty array
      const emptyResult = await supabaseOrderService.updatePaymentStatus([], true);
      console.log("Empty array result:", emptyResult);
      
      // Test with invalid IDs
      const invalidResult = await supabaseOrderService.updatePaymentStatus(["invalid-id-1", "invalid-id-2"], true);
      console.log("Invalid IDs result:", invalidResult);
      
      return { success: true, message: "Invalid input tests completed" };
    } catch (error) {
      console.error("❌ Invalid input test failed:", error);
      return { success: false, error: String(error) };
    }
  },

  /**
   * Test with single order (backward compatibility)
   */
  async testSingleOrderUpdate() {
    console.log("🧪 Testing single order update (backward compatibility)...");
    
    try {
      // Get all orders
      const ordersResult = await supabaseOrderService.getAllOrders();
      
      if (!ordersResult.success || !ordersResult.orders) {
        console.error("❌ Failed to fetch orders:", ordersResult.error);
        return { success: false, error: ordersResult.error };
      }

      // Find one unpaid order
      const unpaidOrder = ordersResult.orders.find(order => !order.isPaid);
      
      if (!unpaidOrder) {
        console.log("ℹ️ No unpaid orders found to test with");
        return { success: true, message: "No unpaid orders to test" };
      }

      console.log(`📋 Testing with single order ID: ${unpaidOrder.id}`);

      // Test with single order in array
      const updateResult = await supabaseOrderService.updatePaymentStatus([unpaidOrder.id], true);
      
      if (updateResult.success) {
        console.log("✅ Single order update successful!");
        return { success: true, updatedOrderId: unpaidOrder.id };
      } else {
        console.error("❌ Single order update failed:", updateResult.error);
        return { success: false, error: updateResult.error };
      }
    } catch (error) {
      console.error("❌ Single order test failed:", error);
      return { success: false, error: String(error) };
    }
  }
};

// Make it available globally for console testing
(window as any).testBulkPaymentUpdate = testBulkPaymentUpdate;

console.log("🔧 Bulk payment update test utilities loaded. Use testBulkPaymentUpdate.testMultipleOrderUpdate() to test.");
