import { useMemo } from "react";
import { useDateFilter } from "./useDateFilter";
import { useOrderFilters } from "./useOrderFilters";
import { useOrderManagement } from "./useOrderManagement";
import { OrderFilterOptions } from "@/services/supabaseOrderService";

/**
 * Combined hook that manages date filtering, order filtering, and data fetching
 * Provides a unified interface for the OrderManagementPage
 */
export function useFilteredOrders() {
  const dateFilter = useDateFilter();
  const orderFilters = useOrderFilters();

  // Create filter options for the service
  const filterOptions: OrderFilterOptions = useMemo(() => {
    const options: OrderFilterOptions = {};

    // Add date filtering
    if (dateFilter.dateFilterType === 'thisWeek') {
      const dateRange = dateFilter.getDateRangeForQuery();
      if (dateRange) {
        options.dateRange = dateRange;
      }
    } else {
      options.dateFilter = dateFilter.getDateForQuery();
    }

    // Add payment status filtering
    options.paymentStatusFilter = orderFilters.paymentStatusFilter;

    return options;
  }, [dateFilter, orderFilters.paymentStatusFilter]);

  // Fetch data with current filters
  const orderManagement = useOrderManagement(filterOptions);

  // Refetch function that uses current filter state
  const refetchWithCurrentFilters = async () => {
    await orderManagement.refetch(filterOptions);
  };

  return {
    // Date filtering
    dateFilter,
    
    // Order filtering
    orderFilters,
    
    // Order management data
    ...orderManagement,
    
    // Override refetch to use current filters
    refetch: refetchWithCurrentFilters,
    
    // Filter options for debugging
    currentFilterOptions: filterOptions,
  };
}
