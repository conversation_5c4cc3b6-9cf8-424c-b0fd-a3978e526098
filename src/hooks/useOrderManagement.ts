import { useState, useEffect } from "react";
import { supabaseOrderService, OrderData, DishSummaryData, OrderFilterOptions } from "@/services/supabaseOrderService";

export interface UseOrderManagementState {
  orders: OrderData[];
  dishSummary: DishSummaryData[];
  isLoading: boolean;
  error: string | null;
  totalOrders: number;
  totalDishes: number;
}

export interface UseOrderManagementReturn extends UseOrderManagementState {
  refetch: (filterOptions?: OrderFilterOptions) => Promise<void>;
  clearError: () => void;
}

/**
 * Custom hook for managing order data fetching from Supabase
 * Provides loading states, error handling, and automatic data aggregation
 * Supports date filtering and payment status filtering
 */
export function useOrderManagement(filterOptions?: OrderFilterOptions): UseOrderManagementReturn {
  const [state, setState] = useState<UseOrderManagementState>({
    orders: [],
    dishSummary: [],
    isLoading: true,
    error: null,
    totalOrders: 0,
    totalDishes: 0,
  });

  const fetchData = async (currentFilterOptions?: OrderFilterOptions) => {
    try {
      console.log("🔄 Starting to fetch order management data...", currentFilterOptions);
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Fetch both orders and dish summary in parallel
      console.log("📡 Fetching orders and dish summary from Supabase...");
      const [ordersResult, dishSummaryResult] = await Promise.all([
        supabaseOrderService.getAllOrders(currentFilterOptions),
        supabaseOrderService.getDishSummary(currentFilterOptions),
      ]);

      console.log("📊 Orders result:", ordersResult);
      console.log("🍽️ Dish summary result:", dishSummaryResult);

      // Handle orders result
      if (!ordersResult.success) {
        throw new Error(ordersResult.error || "Failed to fetch orders");
      }

      // Handle dish summary result
      if (!dishSummaryResult.success) {
        throw new Error(dishSummaryResult.error || "Failed to fetch dish summary");
      }

      const orders = ordersResult.orders || [];
      const dishSummary = dishSummaryResult.dishSummary || [];
      const totalOrders = orders.length;
      const totalDishes = dishSummary.reduce((sum, dish) => sum + dish.quantity, 0);

      setState({
        orders,
        dishSummary,
        isLoading: false,
        error: null,
        totalOrders,
        totalDishes,
      });

      console.log("✅ Order management data fetched successfully", {
        ordersCount: totalOrders,
        dishesCount: dishSummary.length,
        totalDishes,
        orders: orders.slice(0, 2), // Show first 2 orders for debugging
        dishSummary: dishSummary.slice(0, 3), // Show first 3 dishes for debugging
      });
    } catch (error) {
      console.error("❌ Error fetching order management data:", error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      }));
    }
  };

  const refetch = async (currentFilterOptions?: OrderFilterOptions) => {
    await fetchData(currentFilterOptions);
  };

  const clearError = () => {
    setState(prev => ({ ...prev, error: null }));
  };

  // Fetch data on mount
  useEffect(() => {
    fetchData(filterOptions);
  }, [filterOptions]);

  return {
    ...state,
    refetch,
    clearError,
  };
}
