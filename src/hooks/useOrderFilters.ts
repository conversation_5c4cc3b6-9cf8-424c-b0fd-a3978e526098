import { useState, useCallback, useMemo } from "react";

export type PaymentStatusFilter = 'all' | 'paid' | 'unpaid';
export type OrderSortOption = 'totalAmount' | 'orderCount' | 'paymentStatus' | 'userName' | 'recentDate';
export type SortDirection = 'asc' | 'desc';

export interface UseOrderFiltersState {
  paymentStatusFilter: PaymentStatusFilter;
  sortOption: OrderSortOption;
  sortDirection: SortDirection;
  activeFiltersCount: number;
}

export interface UseOrderFiltersReturn extends UseOrderFiltersState {
  setPaymentStatusFilter: (filter: PaymentStatusFilter) => void;
  setSortOption: (option: OrderSortOption) => void;
  setSortDirection: (direction: SortDirection) => void;
  toggleSortDirection: () => void;
  resetFilters: () => void;
  getFilterLabel: (filter: PaymentStatusFilter) => string;
  getSortLabel: (option: OrderSortOption) => string;
  isFilterActive: (filter: PaymentStatusFilter) => boolean;
}

/**
 * Custom hook for managing order filtering and sorting state
 * Provides payment status filtering, sorting options, and utility functions
 */
export function useOrderFilters(): UseOrderFiltersReturn {
  const [paymentStatusFilter, setPaymentStatusFilterState] = useState<PaymentStatusFilter>('all');
  const [sortOption, setSortOptionState] = useState<OrderSortOption>('recentDate');
  const [sortDirection, setSortDirectionState] = useState<SortDirection>('desc');

  // Calculate active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (paymentStatusFilter !== 'all') count++;
    return count;
  }, [paymentStatusFilter]);

  // Set payment status filter
  const setPaymentStatusFilter = useCallback((filter: PaymentStatusFilter) => {
    setPaymentStatusFilterState(filter);
  }, []);

  // Set sort option
  const setSortOption = useCallback((option: OrderSortOption) => {
    setSortOptionState(option);
  }, []);

  // Set sort direction
  const setSortDirection = useCallback((direction: SortDirection) => {
    setSortDirectionState(direction);
  }, []);

  // Toggle sort direction
  const toggleSortDirection = useCallback(() => {
    setSortDirectionState(prev => prev === 'asc' ? 'desc' : 'asc');
  }, []);

  // Reset all filters to default
  const resetFilters = useCallback(() => {
    setPaymentStatusFilterState('all');
    setSortOptionState('recentDate');
    setSortDirectionState('desc');
  }, []);

  // Get human-readable label for payment status filter
  const getFilterLabel = useCallback((filter: PaymentStatusFilter): string => {
    switch (filter) {
      case 'all':
        return 'Tất cả đơn hàng';
      case 'paid':
        return 'Đã thanh toán';
      case 'unpaid':
        return 'Chưa thanh toán';
      default:
        return 'Tất cả đơn hàng';
    }
  }, []);

  // Get human-readable label for sort option
  const getSortLabel = useCallback((option: OrderSortOption): string => {
    switch (option) {
      case 'totalAmount':
        return 'Tổng tiền';
      case 'orderCount':
        return 'Số đơn hàng';
      case 'paymentStatus':
        return 'Trạng thái thanh toán';
      case 'userName':
        return 'Tên người dùng';
      case 'recentDate':
        return 'Ngày gần nhất';
      default:
        return 'Ngày gần nhất';
    }
  }, []);

  // Check if a specific filter is active
  const isFilterActive = useCallback((filter: PaymentStatusFilter): boolean => {
    return paymentStatusFilter === filter;
  }, [paymentStatusFilter]);

  return {
    paymentStatusFilter,
    sortOption,
    sortDirection,
    activeFiltersCount,
    setPaymentStatusFilter,
    setSortOption,
    setSortDirection,
    toggleSortDirection,
    resetFilters,
    getFilterLabel,
    getSortLabel,
    isFilterActive,
  };
}
