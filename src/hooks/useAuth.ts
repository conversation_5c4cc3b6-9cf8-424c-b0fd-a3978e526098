import { useState, useCallback, useEffect } from "react";

const AUTH_USER_ID_KEY = "an-com-auth-user-id";
const AUTH_USER_NAME_KEY = "an-com-auth-user-name";
const AUTH_USER_ROLE_KEY = "an-com-auth-user-role";
const AUTH_TOKEN_KEY = "an-com-auth-token";

export type AuthUser = {
  id: string;
  name: string;
  role: 'admin' | 'user'; 
}

export interface UseAuthReturn {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoaded: boolean;
  login: (user: AuthUser) => void;
  logout: () => void;
}

/**
 * Custom hook for managing authentication state with localStorage persistence
 * Provides login/logout functionality and graceful error handling
 */
export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  /**
   * Safely get user from localStorage
   */
  const getStoredUser = useCallback((): AuthUser | null => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return null;
      }

      const storedId = localStorage.getItem(AUTH_USER_ID_KEY);
      const storedName = localStorage.getItem(AUTH_USER_NAME_KEY);
      const storedRole = localStorage.getItem(AUTH_USER_ROLE_KEY);

      if (!storedId || !storedName || 
          storedId === "null" || storedName === "null" ||
          storedId === "undefined" || storedName === "undefined") {
        return null;
      }

      // Validate that required fields exist and are strings
      if (typeof storedId === 'string' && typeof storedName === 'string' &&
          storedId.trim() && storedName.trim()) {
        const role = storedRole && storedRole !== "null" && storedRole !== "undefined" && 
                    (storedRole === 'admin' || storedRole === 'user') ? storedRole as 'admin' | 'user' : 'user';
        
        return {
          id: storedId.trim(),
          name: storedName.trim(),
          role
        };
      }

      return null;
    } catch (error) {
      console.warn("Failed to read auth user from localStorage:", error);
      return null;
    }
  }, []);

  /**
   * Safely set user in localStorage
   */
  const setStoredUser = useCallback((authUser: AuthUser | null): void => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return;
      }

      if (authUser) {
        localStorage.setItem(AUTH_USER_ID_KEY, authUser.id);
        localStorage.setItem(AUTH_USER_NAME_KEY, authUser.name);
        localStorage.setItem(AUTH_USER_ROLE_KEY, authUser.role || 'user');
        localStorage.setItem(AUTH_TOKEN_KEY, "authenticated"); // Simple token for now
      } else {
        localStorage.removeItem(AUTH_USER_ID_KEY);
        localStorage.removeItem(AUTH_USER_NAME_KEY);
        localStorage.removeItem(AUTH_USER_ROLE_KEY);
        localStorage.removeItem(AUTH_TOKEN_KEY);
      }
    } catch (error) {
      console.warn("Failed to save auth user to localStorage:", error);
    }
  }, []);

  /**
   * Load user from localStorage on mount
   */
  useEffect(() => {
    const storedUser = getStoredUser();
    setUser(storedUser);
    setIsLoaded(true);
  }, [getStoredUser]);

  /**
   * Login user and persist to localStorage
   */
  const login = useCallback((authUser: AuthUser) => {
    setUser(authUser);
    console.log("%c 🐈: login -> authUser ", "font-size:16px;background-color:#e0bd1e;color:black;", authUser)
    setStoredUser(authUser);
  }, [setStoredUser]);

  /**
   * Logout user and clear localStorage
   */
  const logout = useCallback(() => {
    setUser(null);
    setStoredUser(null);
  }, [setStoredUser]);

  const isAuthenticated = user !== null;

  return {
    user,
    isAuthenticated,
    isLoaded,
    login,
    logout,
  };
}
