import { useState, useEffect, useCallback } from 'react';
import { DashboardData } from '@/types/food';
import { dashboardService } from '@/services/dashboardService';

export interface UseDashboardDataReturn {
  data: DashboardData;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useDashboardData = (autoRefresh: boolean = true): UseDashboardDataReturn => {
  const [data, setData] = useState<DashboardData>({
    stats: {
      totalRevenue: 0,
      totalOrders: 0,
      totalDebt: 0,
      paidOrders: 0,
      unpaidOrders: 0,
      topItems: []
    },
    categoryData: [],
    weeklyData: [],
    isLoading: true,
    error: null
  });

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const dashboardData = await dashboardService.getDashboardData();

      setData(dashboardData);
      setError(dashboardData.error);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Không thể tải dữ liệu dashboard';
      setError(errorMessage);
      console.error('Error fetching dashboard data:', err);

      // Set fallback data on complete failure
      setData({
        stats: {
          totalRevenue: 0,
          totalOrders: 0,
          totalDebt: 0,
          paidOrders: 0,
          unpaidOrders: 0,
          topItems: []
        },
        categoryData: [],
        weeklyData: [],
        isLoading: false,
        error: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Auto-refresh every 30 seconds if enabled
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchData();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, fetchData]);

  return {
    data: {
      ...data,
      isLoading,
      error
    },
    isLoading,
    error,
    refetch
  };
};
