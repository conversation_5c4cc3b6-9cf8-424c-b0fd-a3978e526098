import { useState, useEffect, useCallback } from "react";

const CUSTOMER_NAME_KEY = "food-order-customer-name";
const AUTH_USER_NAME_KEY = "an-com-auth-user-name";

export interface UseCustomerNameReturn {
  customerName: string;
  setCustomerName: (name: string) => void;
  loadCustomerName: () => void;
  saveCustomerName: (name: string) => void;
  clearCustomerName: () => void;
  isLoaded: boolean;
}

/**
 * Custom hook for managing customer name persistence in localStorage
 * Provides auto-fill functionality and graceful error handling
 * Now integrates with authentication system to auto-populate authenticated user names
 */
export function useCustomerName(): UseCustomerNameReturn {
  const [customerName, setCustomerNameState] = useState<string>("");
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  /**
   * Safely get customer name from localStorage
   * Prioritizes authenticated user name, falls back to saved customer name
   */
  const getStoredCustomerName = useCallback((): string => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return "";
      }

      // First, check if user is authenticated and get their name
      const authUserName = localStorage.getItem(AUTH_USER_NAME_KEY);
      if (authUserName && authUserName !== "null" && authUserName !== "undefined") {
        return authUserName.trim();
      }

      // Fall back to saved customer name
      const stored = localStorage.getItem(CUSTOMER_NAME_KEY);
      console.log("%c 👮‍♂️: functionuseCustomerName -> stored ", "font-size:16px;background-color:#90763f;color:white;", stored)
      return stored && stored !== "null" && stored !== "undefined" ? stored : "";
    } catch (error) {
      console.warn("Failed to read customer name from localStorage:", error);
      return "";
    }
  }, []);

  /**
   * Safely set value in localStorage (only for non-authenticated users)
   */
  const setStoredCustomerName = useCallback((name: string): void => {
    try {
      if (typeof window === "undefined" || !window.localStorage) {
        return;
      }

      // Don't save to customer name storage if user is authenticated
      const authUserName = localStorage.getItem(AUTH_USER_NAME_KEY);
      if (authUserName && authUserName !== "null" && authUserName !== "undefined") {
        return; // User is authenticated, don't override with customer name
      }

      if (name && name.trim()) {
        localStorage.setItem(CUSTOMER_NAME_KEY, name.trim());
      } else {
        localStorage.removeItem(CUSTOMER_NAME_KEY);
      }
    } catch (error) {
      console.warn("Failed to save customer name to localStorage:", error);
    }
  }, []);

  /**
   * Load customer name from localStorage on mount
   */
  // useEffect(() => {
  //   const storedName = getStoredCustomerName();
  //   setCustomerNameState(storedName);
  //   setIsLoaded(true);
  // }, [getStoredCustomerName]);
  // load when dialog open
  const loadCustomerName = useCallback(() => {
    const userName = getStoredCustomerName();
    setCustomerNameState(userName);
    setIsLoaded(true);
  }, [getStoredCustomerName]);

  /**
   * Update customer name in state (for form input)
   */
  const setCustomerName = useCallback((name: string) => {
    setCustomerNameState(name);
  }, []);

  /**
   * Save customer name to localStorage (called after successful order)
   */
  const saveCustomerName = useCallback((name: string) => {
    const trimmedName = name.trim();
    if (trimmedName) {
      setStoredCustomerName(trimmedName);
      setCustomerNameState(trimmedName);
    }
  }, [setStoredCustomerName]);

  /**
   * Clear customer name from both state and localStorage
   */
  const clearCustomerName = useCallback(() => {
    setCustomerNameState("");
    setStoredCustomerName("");
  }, [setStoredCustomerName]);

  return {
    customerName,
    loadCustomerName,
    setCustomerName,
    saveCustomerName,
    clearCustomerName,
    isLoaded,
  };
}
