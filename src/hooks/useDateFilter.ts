import { useState, useCallback, useMemo } from "react";
import { format, startOfDay, subDays, startOfWeek, endOfWeek, isToday, isYesterday, isThisWeek } from "date-fns";

export type DateFilterType = 'today' | 'yesterday' | 'thisWeek' | 'custom';

export interface UseDateFilterState {
  selectedDate: Date;
  dateFilterType: DateFilterType;
  formattedDate: string;
  isToday: boolean;
  isYesterday: boolean;
  isThisWeek: boolean;
}

export interface UseDateFilterReturn extends UseDateFilterState {
  setSelectedDate: (date: Date) => void;
  setToday: () => void;
  setYesterday: () => void;
  setThisWeek: () => void;
  getDateForQuery: () => string;
  getDateRangeForQuery: () => { startDate: string; endDate: string } | null;
  resetToToday: () => void;
}

/**
 * Custom hook for managing date filtering state in order management
 * Provides date selection, navigation, and formatting utilities
 */
export function useDateFilter(): UseDateFilterReturn {
  const [selectedDate, setSelectedDateState] = useState<Date>(() => startOfDay(new Date()));
  const [dateFilterType, setDateFilterType] = useState<DateFilterType>('today');

  // Memoized computed values
  const computedValues = useMemo(() => {
    const today = startOfDay(new Date());
    const yesterday = startOfDay(subDays(new Date(), 1));
    
    return {
      formattedDate: format(selectedDate, 'dd/MM/yyyy'),
      isToday: isToday(selectedDate),
      isYesterday: isYesterday(selectedDate),
      isThisWeek: isThisWeek(selectedDate),
      today,
      yesterday,
    };
  }, [selectedDate]);

  // Set selected date and determine filter type
  const setSelectedDate = useCallback((date: Date) => {
    const normalizedDate = startOfDay(date);
    setSelectedDateState(normalizedDate);
    
    // Determine the filter type based on the selected date
    if (isToday(normalizedDate)) {
      setDateFilterType('today');
    } else if (isYesterday(normalizedDate)) {
      setDateFilterType('yesterday');
    } else if (isThisWeek(normalizedDate)) {
      setDateFilterType('thisWeek');
    } else {
      setDateFilterType('custom');
    }
  }, []);

  // Quick navigation functions
  const setToday = useCallback(() => {
    const today = startOfDay(new Date());
    setSelectedDateState(today);
    setDateFilterType('today');
  }, []);

  const setYesterday = useCallback(() => {
    const yesterday = startOfDay(subDays(new Date(), 1));
    setSelectedDateState(yesterday);
    setDateFilterType('yesterday');
  }, []);

  const setThisWeek = useCallback(() => {
    // For "this week" we'll use today as the selected date but mark it as thisWeek type
    const today = startOfDay(new Date());
    setSelectedDateState(today);
    setDateFilterType('thisWeek');
  }, []);

  // Get date string for database queries (YYYY-MM-DD format)
  const getDateForQuery = useCallback(() => {
    return format(selectedDate, 'yyyy-MM-dd');
  }, [selectedDate]);

  // Get date range for "this week" queries
  const getDateRangeForQuery = useCallback(() => {
    if (dateFilterType === 'thisWeek') {
      const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Monday start
      const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 1 }); // Sunday end
      
      return {
        startDate: format(weekStart, 'yyyy-MM-dd'),
        endDate: format(weekEnd, 'yyyy-MM-dd'),
      };
    }
    return null;
  }, [selectedDate, dateFilterType]);

  // Reset to today
  const resetToToday = useCallback(() => {
    setToday();
  }, [setToday]);

  return {
    selectedDate,
    dateFilterType,
    formattedDate: computedValues.formattedDate,
    isToday: computedValues.isToday,
    isYesterday: computedValues.isYesterday,
    isThisWeek: computedValues.isThisWeek,
    setSelectedDate,
    setToday,
    setYesterday,
    setThisWeek,
    getDateForQuery,
    getDateRangeForQuery,
    resetToToday,
  };
}
