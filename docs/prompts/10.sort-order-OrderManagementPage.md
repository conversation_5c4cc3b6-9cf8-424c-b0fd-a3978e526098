
I want to add a sort button next to the existing "Lịch sử chung" (General History) button in the OrderManagementPage component located at `src/pages/OrderManagementPage.tsx`. 

The sort button should:
1. Be positioned immediately next to the "Lịch sử chung" button in the UI
2. Allow users to sort the displayed orders by relevant criteria (such as order date, payment status, customer name, or total amount)
3. Follow the existing UI design patterns used in the component (using Tailwind CSS, lucide-react icons, and consistent styling)
4. Provide a dropdown or toggle mechanism to select different sorting options
5. Update the order list display in real-time when a sort option is selected
6. Maintain the current filtering functionality while adding the sorting capability

Please implement this sort functionality while preserving the existing payment status management features that were previously implemented in the component.