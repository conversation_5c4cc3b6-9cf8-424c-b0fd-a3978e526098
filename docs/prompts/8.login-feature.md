I want to implement a simple login feature for the an-com-dashboard application with the following requirements:

**UI Components:**
1. Add a login icon (using lucide-react icons) to the existing `src/components/Header.tsx` component. if login already, show mypage icon. let use modern icon for best ui
2. When the login icon is clicked, display a modal dialog for user authentication
3. The login form should contain:
   - Name field (supporting Vietnamese and Japanese character input)
   - Password field
   - Submit button
   - Cancel/Close button
4. Use the existing design patterns with Tailwind CSS, card-based layout, and smooth transitions consistent with the current UI

**Database Changes:**
5. Add a password column to the users table using: `ALTER TABLE users ADD COLUMN password TEXT;`
6. Ensure the password field allows NULL values initially for existing users

**Functionality:**
7. Implement form validation for both name and password fields
8. Handle login authentication by checking the name and password against the users table
9. Provide appropriate success/error feedback to the user
10. Store login state using localStorage with a custom React hook (following existing patterns)
11. Handle graceful error cases (database connection issues, invalid credentials, etc.)

**Technical Requirements:**
- Follow camelCase naming convention
- Use existing database connection patterns
- Implement proper error handling
- Ensure the modal can be closed by clicking outside or pressing ESC
- Make the login state persistent across page refreshes

## ✅ Implementation Summary
The authentication and order confirmation system has been upgraded to **seamlessly integrate user identity** with customer name handling. Here's what was implemented:
### 🔄 Changes in `src/hooks/useAuth.ts`
* Replaced the old full-object storage with **separate keys**:
  * `an-com-auth-user-id` → stores user ID (e.g., `"65bf5917-41c0-4c6f-9f69-07a26ee83776"`)
  * `an-com-auth-user-name` → stores user name (e.g., `"Mạnh Công"`)
* `getStoredUser()` and `setStoredUser()` updated to use the new structure.
* Internal storage format changed, but returned object still conforms to `AuthUser`.
### 🔄 Changes in `src/hooks/useCustomerName.ts`
* Automatically uses the name from `an-com-auth-user-name` if present.
* Falls back to `food-order-customer-name` only when user is **not authenticated**.
* Prevents saving guest names if an authenticated user is active.
* Enables persistence of guest names only for unauthenticated users.
#### 🔍 Sample Logic:
```ts
const authUserName = localStorage.getItem(AUTH_USER_NAME_KEY);
if (authUserName && authUserName !== "null" && authUserName !== "undefined") {
  return authUserName.trim();
}
// Fallback to guest customer name
```
* Automatically fills the customer name field with the authenticated name when available.
* Removed explicit `loadCustomerName()` calls — the hook now auto-loads on mount or state change.
* Corrected `saveCustomerName()` usage to avoid overwriting authenticated names.
