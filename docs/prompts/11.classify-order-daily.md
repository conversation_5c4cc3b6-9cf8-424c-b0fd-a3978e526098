Enhance the OrderManagementPage component at `src/pages/OrderManagementPage.tsx` with comprehensive date filtering and order management functionality.

**Current State Analysis:**
- The page currently displays all orders from the database combined by user across all dates
  1. 👥 Đơn đặt theo người dùng
  2. 📊 Tổng hợp món đã đặt
- Orders are grouped by user regardless of date, showing cumulative data
- No date filtering or historical data viewing capabilities exist

**Required Functionality Enhancements:**

1. **Date-Based Order Display (Primary Requirement)**:
   - **Default View**: Show only today's orders by default (filter by `order_date = TODAY()`)
   - **Date Picker Integration**: Add a calendar/date picker component to allow users to select and view orders from specific historical dates
   - **Daily Order Grouping**: Modify the current user-based grouping to be scoped per selected day (users should be grouped separately for each day)
   - **Data Persistence**: Maintain selected date in component state and allow easy switching between dates

2. **Enhanced Filtering & Sorting Controls**:
   - **Payment Status Filter**: Add toggle buttons or dropdown to filter orders by payment status (`is_paid` field):
     - "All Orders" (default)
     - "Paid Orders Only" (`is_paid = true`)
     - "Unpaid Orders Only" (`is_paid = false`)
   - **Date Navigation**: Add quick navigation buttons for "Today", "Yesterday", "This Week"
   - **Sort Options**: Add sorting controls for orders within each day (by total amount, order count, payment status)

3. **UI/UX Implementation Requirements**:
   - **Calendar Component**: Integrate a date picker component (use existing UI library components like shadcn/ui Calendar)
   - **Filter Controls**: Add a filter bar above the order cards with clear visual indicators for active filters
   - **Loading States**: Show appropriate loading indicators when switching between dates
   - **Empty States**: Display meaningful messages when no orders exist for selected date/filters
   - **Date Display**: Clearly show the currently selected date in the page header

4. **Technical Implementation Specifications**:
   - **Service Layer Updates**: Modify `supabaseOrderService.getAllOrders()` to accept date parameters and payment status filters
   - **Query Optimization**: Use efficient database queries with proper date filtering (`WHERE DATE(order_date) = ?`)
   - **State Management**: Create custom hooks for date selection and filter management
   - **Component Architecture**: Split functionality into smaller, reusable components:
     - `DateFilterControls` component for date picker and quick navigation
     - `PaymentStatusFilter` component for payment filtering
     - `OrderSortControls` component for sorting options
     - Custom hooks: `useDateFilter`, `useOrderFilters`, `useFilteredOrders`

5. **Database Integration Requirements**:
   - **Understand Current Schema**: The `orders` table contains:
     - `order_date`: Date field for filtering orders by day
     - `is_paid`: Boolean field for payment status filtering
     - `total_price`: Numeric field for sorting by amount
   - **Query Modifications**: Update database queries to include date range filtering and payment status filtering
   - **Performance Considerations**: Ensure queries are optimized with proper indexing on `order_date` and `is_paid` fields

6. **Code Quality & Consistency Standards**:
   - Follow camelCase naming convention for all new variables, functions, and component props
   - Create small, focused utility functions and custom hooks to avoid code duplication
   - Maintain consistency with existing component structure and Tailwind CSS styling patterns
   - Add proper TypeScript interfaces for new filter and date-related data structures
   - Implement proper error handling for date parsing and invalid date selections

**Expected User Flow:**
1. User opens OrderManagementPage → sees today's orders by default
2. User clicks calendar icon → date picker opens
3. User selects historical date → page updates to show orders for that specific date
4. User applies payment status filter → orders are filtered accordingly
5. User can sort orders within the selected date by various criteria

**Testing Requirements:**
- Test date filtering with various date selections (today, past dates, future dates)
- Verify payment status filtering works correctly with database queries
- Test edge cases: no orders for selected date, invalid date selections
- Ensure proper loading states and error handling
- Validate that date changes don't break existing bulk payment functionality
- Run `npm run lint` to ensure code quality standards



**Database Schema Reference:**
- `users`: id, name, role (VARCHAR: 'admin'|'user'), created_at, updated_at, deleted_at
- `orders`: id, user_id, total_price, is_paid (BOOLEAN), order_date, created_at, updated_at, deleted_at
- `products`: id, name, price, category, created_at, updated_at, deleted_at
- `order_items`: id, order_id, product_id, quantity, created_at, updated_at, deleted_at
- `order_history`: id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, created_at, updated_at, deleted_at
- `debts`: id, user_id, amount, created_at, updated_at, deleted_at
