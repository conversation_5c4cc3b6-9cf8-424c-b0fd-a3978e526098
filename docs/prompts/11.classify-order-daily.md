Enhance the OrderManagementPage component at `src/pages/OrderManagementPage.tsx` with comprehensive date filtering and order management functionality.

**Current State Analysis:**
- The page currently displays all orders from the database combined by user across all dates
  1. 👥 Đơn đặt theo người dùng
  2. 📊 Tổng hợp món đã đặt
- Orders are grouped by user regardless of date, showing cumulative data
- No date filtering or historical data viewing capabilities exist

**Required Functionality Enhancements:**

1. **Date-Based Order Display (Primary Requirement)**:
   - **Default View**: Show only today's orders by default (filter by `order_date = TODAY()`)
   - **Date Picker Integration**: Add a calendar/date picker component to allow users to select and view orders from specific historical dates
   - **Daily Order Grouping**: Modify the current user-based grouping to be scoped per selected day (users should be grouped separately for each day)
   - **Data Persistence**: Maintain selected date in component state and allow easy switching between dates

2. **Enhanced Filtering & Sorting Controls**:
   - **Payment Status Filter**: Add toggle buttons or dropdown to filter orders by payment status (`is_paid` field):
     - "All Orders" (default)
     - "Paid Orders Only" (`is_paid = true`)
     - "Unpaid Orders Only" (`is_paid = false`)
   - **Date Navigation**: Add quick navigation buttons for "Today", "Yesterday", "This Week"
   - **Sort Options**: Add sorting controls for orders within each day (by total amount, order count, payment status)

3. **UI/UX Implementation Requirements**:
   - **Calendar Component**: Integrate a date picker component (use existing UI library components like shadcn/ui Calendar)
   - **Filter Controls**: Add a filter bar above the order cards with clear visual indicators for active filters
   - **Loading States**: Show appropriate loading indicators when switching between dates
   - **Empty States**: Display meaningful messages when no orders exist for selected date/filters
   - **Date Display**: Clearly show the currently selected date in the page header

4. **Technical Implementation Specifications**:
   - **Service Layer Updates**: Modify `supabaseOrderService.getAllOrders()` to accept date parameters and payment status filters
   - **Query Optimization**: Use efficient database queries with proper date filtering (`WHERE DATE(order_date) = ?`)
   - **State Management**: Create custom hooks for date selection and filter management
   - **Component Architecture**: Split functionality into smaller, reusable components:
     - `DateFilterControls` component for date picker and quick navigation
     - `PaymentStatusFilter` component for payment filtering
     - `OrderSortControls` component for sorting options
     - Custom hooks: `useDateFilter`, `useOrderFilters`, `useFilteredOrders`

5. **Database Integration Requirements**:
   - **Understand Current Schema**: The `orders` table contains:
     - `order_date`: Date field for filtering orders by day
     - `is_paid`: Boolean field for payment status filtering
     - `total_price`: Numeric field for sorting by amount
   - **Query Modifications**: Update database queries to include date range filtering and payment status filtering
   - **Performance Considerations**: Ensure queries are optimized with proper indexing on `order_date` and `is_paid` fields

6. **Code Quality & Consistency Standards**:
   - Follow camelCase naming convention for all new variables, functions, and component props
   - Create small, focused utility functions and custom hooks to avoid code duplication
   - Maintain consistency with existing component structure and Tailwind CSS styling patterns
   - Add proper TypeScript interfaces for new filter and date-related data structures
   - Implement proper error handling for date parsing and invalid date selections

**Expected User Flow:**
1. User opens OrderManagementPage → sees today's orders by default
2. User clicks calendar icon → date picker opens
3. User selects historical date → page updates to show orders for that specific date
4. User applies payment status filter → orders are filtered accordingly
5. User can sort orders within the selected date by various criteria

**Testing Requirements:**
- Test date filtering with various date selections (today, past dates, future dates)
- Verify payment status filtering works correctly with database queries
- Test edge cases: no orders for selected date, invalid date selections
- Ensure proper loading states and error handling
- Validate that date changes don't break existing bulk payment functionality
- Run `npm run lint` to ensure code quality standards



**Database Schema Reference:**
- `users`: id, name, role (VARCHAR: 'admin'|'user'), created_at, updated_at, deleted_at
- `orders`: id, user_id, total_price, is_paid (BOOLEAN), order_date, created_at, updated_at, deleted_at
- `products`: id, name, price, category, created_at, updated_at, deleted_at
- `order_items`: id, order_id, product_id, quantity, created_at, updated_at, deleted_at
- `order_history`: id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, created_at, updated_at, deleted_at
- `debts`: id, user_id, amount, created_at, updated_at, deleted_at

--- 
Bug Report.
**Bug Report: Date Filtering Not Working for "Today" and "Yesterday" Buttons in OrderManagementPage**

**Issue Description:**
The date filtering functionality in the OrderManagementPage is not working correctly when users click the "Today" and "Yesterday" quick navigation buttons. The page should display filtered data based on the selected date, but it's not showing the correct orders.

**Expected Behavior:**
When users click date filter buttons, both sections should update to show only orders from the selected date:
1. **👥 Đơn đặt theo người dùng** (Orders grouped by user) - should show only users who placed orders on the selected date
2. **📊 Tổng hợp món đã đặt** (Dish summary) - should show only dishes ordered on the selected date

**Current Database Data:**
```json
[
    {
        "id": "1cbc2465-8181-4766-a3f0-9053e841f879",
        "userName": "nmcong1",
        "dishes": ["ポテト　サラダ", "そぼろ"],
        "totalAmount": 500,
        "isPaid": false,
        "orderDate": "2025-07-23"
    },
    {
        "id": "aa4fe7d0-ff67-44eb-8689-a92931c08c17",
        "userName": "lhbinh1",
        "dishes": ["のり (Thập Cẩm - Nhỏ)", "みそ汁"],
        "totalAmount": 500,
        "isPaid": false,
        "orderDate": "2025-07-22"
    }
]
```

**API Endpoints Being Used:**

1. **Orders API:** `GET /orders`
   - Query parameters for date filtering:
     - `order_date=gte.2025-07-23T00:00:00.000Z`
     - `order_date=lt.2025-07-23T23:59:59.999Z`
   - Select fields: `id,total_price,is_paid,order_date,created_at,users!orders_user_id_fkey(name),order_items(quantity,products(name))`

2. **Order Items API:** `GET /order_items`
   - Select fields: `quantity,products(name,price),orders!order_items_order_id_fkey(deleted_at,order_date,is_paid)`
   - Filter: `deleted_at=is.null`

**Required Analysis & Fix:**
1. **Debug the date filtering logic** in the `useDateFilter` hook and verify that `getDateForQuery()` returns the correct date format
2. **Check the API query construction** in `supabaseOrderService.getAllOrders()` and `getDishSummary()` methods
3. **Verify the date range filtering** is correctly applied to both orders and order_items queries
4. **Test with various date selections** including:
   - Today (2025-07-23)
   - Yesterday (2025-07-22) 
   - Past dates
   - Future dates (should show empty results)
5. **Ensure both UI sections update correctly** when date filters change
6. **Validate the filterOptions object** is being passed correctly to the database queries

**Success Criteria:**
- Clicking "Today" shows only nmcong1's order (orderDate: 2025-07-23)
- Clicking "Yesterday" shows only lhbinh1's order (orderDate: 2025-07-22)
- Both user grouping and dish summary sections reflect the filtered date
- Date picker calendar selection works for any historical date
- Empty states display correctly when no orders exist for selected dates