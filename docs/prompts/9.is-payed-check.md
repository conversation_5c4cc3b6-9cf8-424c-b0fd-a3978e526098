Implement comprehensive payment status management functionality in the OrderManagementPage component located at `src/pages/OrderManagementPage.tsx`.

**Database Schema Updates Required:**
1. **Users Table Enhancement**: Add a `role` field to the `users` table with values 'admin' or 'user' to distinguish between regular users and admin users
2. **Orders Table Verification**: Confirm the existing `orders` table contains the `is_paid` boolean field (should already exist based on current schema)

**UI/UX Implementation Requirements:**

1. **Payment Status Visual Indicators**:
   - Display payment status prominently within each order card's content area
   - For unpaid orders (`is_paid = false`): Show "Chưa thanh toán" with red/warning styling (use Tailwind classes like `text-red-600`, `bg-red-50`)
   - For paid orders (`is_paid = true`): Show "Đã thanh toán hết" with green/success styling (use Tailwind classes like `text-green-600`, `bg-green-50`)
   - Consider using a badge/pill component for consistent visual treatment
   - Ensure payment status is also reflected in the OrderHistoryDialog component

2. **Admin-Only Payment Action Button**:
   - Add a "Mark as Paid" button positioned next to the existing "History" button in each order card's action area
   - Button visibility and functionality must be restricted to users with `role = 'admin'`
   - Use lucide-react icons (e.g., `CheckCircle` or `CreditCard`) with consistent button styling
   - Button states:
     - Enabled: For unpaid orders when user is admin
     - Disabled/Hidden: For already paid orders or when user is not admin
   - Apply hover effects and transitions consistent with existing UI patterns

3. **Payment Confirmation Modal Dialog**:
   - Create a reusable confirmation dialog component following existing modal patterns
   - Dialog content should include:
     - Clear heading: "Confirm Payment"
     - Order details: Order ID, customer name, total amount
     - Confirmation message: "Mark this order as paid?"
   - Action buttons: "Cancel" (secondary) and "Confirm Payment" (primary with success styling)
   - Implement proper modal backdrop, focus management, and ESC key handling

**Technical Implementation Specifications:**

1. **Authentication & Role-Based Authorization**:
   - Retrieve current user's role from localStorage (key: 'user-role') or authentication context
   - Implement conditional rendering: `{userRole === 'admin' && <PaymentButton />}`
   - Add client-side role validation before showing payment functionality
   - Include server-side role verification for payment update operations

2. **State Management & Data Flow**:
   - Use React hooks pattern: `useState` for component state, `useEffect` for data fetching
   - Implement loading states during payment operations with loading spinners/disabled buttons
   - Add comprehensive error handling with user-friendly error messages displayed via toast notifications or inline alerts
   - Implement optimistic UI updates with rollback capability on failure

3. **Database Integration**:
   - Create/update API endpoint to handle payment status updates: `PATCH /api/orders/:orderId/payment`
   - Database operation: `UPDATE orders SET is_paid = true, updated_at = NOW() WHERE id = :orderId`
   - Add proper error handling for database connection failures, constraint violations, and transaction rollbacks
   - Implement data refresh mechanism to update UI state after successful payment marking
   - Consider adding audit trail entry to `order_history` table for payment status changes

4. **Code Quality & Consistency Standards**:
   - Follow camelCase naming convention for all variables, functions, and component props
   - Create reusable components:
     - `PaymentStatusBadge` component for status display
     - `PaymentConfirmationDialog` component for confirmation modal
     - `AdminPaymentButton` component for the payment action button
   - Maintain consistency with existing codebase patterns, especially component structure and styling approaches
   - Add proper TypeScript types for payment-related data structures

**Expected User Interaction Flow:**
1. **Admin User Experience**:
   - Admin logs in and navigates to Order Management page
   - Admin sees payment status indicators on all order cards
   - For unpaid orders, admin sees enabled "Mark as Paid" button
   - Admin clicks payment button → PaymentConfirmationDialog opens with order details
   - Admin clicks "Confirm Payment" → loading state shown → database updated → UI refreshes
   - Payment button becomes disabled/hidden for now-paid order
   - Payment status change is reflected in OrderHistoryDialog

2. **Regular User Experience**:
   - Regular user sees payment status indicators but no payment action buttons
   - All payment management functionality remains hidden/inaccessible

**Database Schema Reference:**
- `users`: id, name, role (VARCHAR: 'admin'|'user'), created_at, updated_at, deleted_at
- `orders`: id, user_id, total_price, is_paid (BOOLEAN), order_date, created_at, updated_at, deleted_at
- `products`: id, name, price, category, created_at, updated_at, deleted_at
- `order_items`: id, order_id, product_id, quantity, created_at, updated_at, deleted_at
- `order_history`: id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, created_at, updated_at, deleted_at
- `debts`: id, user_id, amount, created_at, updated_at, deleted_at

**Testing & Validation:**
- Use Supabase CLI to verify database schema changes and test data operations
- Test role-based access control with both admin and regular user accounts
- Verify payment status updates persist correctly in database
- Test error scenarios (network failures, unauthorized access, invalid data)
- Run "npm run lint" to check typescript lint of project.

** Coding rules
- use small methods


I need to refactor the `updatePaymentStatus()` method in `src/services/supabaseOrderService.ts` to handle bulk payment status updates:

**Current Issue**: The method currently accepts a single `orderId` parameter but the code is already trying to use it as an array (there's a type mismatch - parameter is `string[]` but the `.eq()` method expects a single value).

**Required Changes**:
1. **Fix the supabaseOrderService.ts method**:
   - The `updatePaymentStatus(orderId: string[], isPaid: boolean)` method should properly handle an array of order IDs
   - Use the correct Supabase query method to update multiple records (likely `.in()` instead of `.eq()`)
   - Ensure the database update sets `is_paid = true` and `updated_at` timestamp for all specified order IDs
   - Maintain existing error handling and return format

2. **Update OrderManagementPage.tsx**:
   - Find where `updatePaymentStatus()` is called
   - Ensure the calling code passes an array of order IDs (even if it's a single ID in an array)
   - Verify the UI properly handles the bulk update functionality
   - Test that admin users can mark payments as paid for single or multiple orders

**Context**: This is for an admin interface where users can mark orders as paid, and we want to support bulk operations for efficiency.

Please first examine both files to understand the current implementation, then make the necessary changes to support proper bulk payment status updates.