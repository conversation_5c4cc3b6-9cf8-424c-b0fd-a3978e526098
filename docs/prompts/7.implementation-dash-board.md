Refactor the DashboardPage component located at `src/pages/DashboardPage.tsx` to integrate with a PostgreSQL database and display comprehensive dashboard analytics.

**Requirements:**
1. **Database Integration**: Connect to and fetch data from the PostgreSQL database with the following schema:
   - `users` - Customer information (id, name, timestamps, soft delete)
   - `products` - Japanese dishes menu (id, name, price, category, timestamps, soft delete)
   - `orders` - Order records (id, user_id, total_price, is_paid, order_date, timestamps, soft delete)
   - `order_items` - Order line items (id, order_id, product_id, quantity, timestamps, soft delete)
   - `order_history` - Complete audit trail (id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, timestamps, soft delete)
   - `debts` - Customer debt tracking (id, user_id, amount, timestamps, soft delete)

2. **Dashboard Content**: Display key business metrics and data visualizations including:
   - Total revenue and order statistics
   - Customer metrics (total customers, active customers)
   - Product performance (popular items, categories)
   - Payment status overview (paid vs unpaid orders)
   - Outstanding debts summary
   - Recent order activity

3. **UI/UX Requirements**: 
   - Use card-based layout with clean, modern design
   - Implement Tailwind CSS for styling with gradient backgrounds and white text
   - Add lucide-react icons for visual elements
   - Include smooth hover effects and transitions
   - Ensure responsive design for different screen sizes

4. **Technical Implementation**:
   - Use React hooks for state management
   - Implement proper error handling for database operations
   - Add loading states while fetching data
   - Follow camelCase naming convention
   - Create reusable components where appropriate

5. **Data Handling**:
   - Respect soft delete flags (exclude deleted_at records)
   - Handle potential null/undefined values gracefully
   - Implement data refresh functionality