-- Add password column to users table for authentication
-- This migration adds a password field to support login functionality

-- Add password column to users table
ALTER TABLE users ADD COLUMN password TEXT;

-- Create index for efficient querying by name (for login lookups)
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name) WHERE deleted_at IS NULL;

-- Add comment to document the password column
COMMENT ON COLUMN users.password IS 'User password for authentication. NULL values allowed for existing users.';

-- Update the updated_at timestamp trigger to include password changes
-- (The trigger should already exist from the original users table creation)

-- Note: Password should be hashed before storing in production
-- For now, we'll store plain text passwords for simplicity as requested
