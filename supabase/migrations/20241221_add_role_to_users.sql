-- Add role column to users table for role-based access control
-- This migration adds a role field to distinguish between admin and regular users

-- Add role column to users table with default value 'user'
ALTER TABLE users ADD COLUMN role TEXT CHECK (role IN ('admin', 'user')) DEFAULT 'user';
ALTER TABLE users ADD CONSTRAINT users_name_unique UNIQUE (name);
-- Update the role column to be NOT NULL with default value
ALTER TABLE users ALTER COLUMN role SET NOT NULL;

-- Create index for efficient querying by role
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role) WHERE deleted_at IS NULL;

-- Add comment to document the role column
COMMENT ON COLUMN users.role IS 'User role for access control. Values: admin, user. Default: user';

-- Insert a default admin user for testing (optional - can be removed in production)
-- Password should be changed in production
INSERT INTO users (name, password, role) 
VALUES ('admin', 'cmc1234@', 'admin')
ON CONFLICT (name) DO UPDATE SET role = 'admin';

